import { useState, useEffect, useCallback } from 'react';
import { GetTasks, CreateTask, StartTask, StopTask, DeleteTask, GetTaskResults } from '../wailsjs/go/main/App';

/**
 * 任务数据管理Hook - 使用真实后端API
 */
export function useTaskData() {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 获取任务数据
  const fetchTasks = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const taskList = await GetTasks();
      // 统一 id 字段并格式化时间
      const tasksWithId = taskList.map(task => ({
        ...task,
        id: task.ID || task.id,
        createdAt: task.createdAt ? new Date(task.createdAt) : new Date(),
        startedAt: task.startedAt ? new Date(task.startedAt) : null,
        completedAt: task.completedAt ? new Date(task.completedAt) : null
      }));
      setTasks(tasksWithId);
    } catch (err) {
      setError(err.message || '加载任务列表失败');
      console.error('Failed to load tasks:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始加载
  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  // 创建任务
  const createTask = useCallback(async (taskData) => {
    try {
      setError(null);
      
      const newTask = await CreateTask({
        name: taskData.name,
        description: taskData.description,
        serverIds: taskData.serverIds || []
      });
      
      // 重新加载任务列表
      await fetchTasks();
      return newTask;
    } catch (err) {
      setError(err.message || '创建任务失败');
      throw err;
    }
  }, [fetchTasks]);

  // 启动任务
  const startTask = useCallback(async (taskId) => {
    try {
      setError(null);
      
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      await StartTask(task.ID || task.id);
      
      // 重新加载任务列表
      await fetchTasks();
    } catch (err) {
      setError(err.message || '启动任务失败');
      throw err;
    }
  }, [tasks, fetchTasks]);

  // 停止任务
  const stopTask = useCallback(async (taskId) => {
    try {
      setError(null);
      
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      await StopTask(task.ID || task.id);
      
      // 重新加载任务列表
      await fetchTasks();
    } catch (err) {
      setError(err.message || '停止任务失败');
      throw err;
    }
  }, [tasks, fetchTasks]);

  // 删除任务
  const deleteTask = useCallback(async (taskId) => {
    try {
      setError(null);
      
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      await DeleteTask(task.ID || task.id);
      
      // 重新加载任务列表
      await fetchTasks();
    } catch (err) {
      setError(err.message || '删除任务失败');
      throw err;
    }
  }, [tasks, fetchTasks]);

  // 获取任务结果
  const getTaskResults = useCallback(async (taskId) => {
    try {
      setError(null);
      
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      const results = await GetTaskResults(task.ID || task.id);
      return results;
    } catch (err) {
      setError(err.message || '获取任务结果失败');
      throw err;
    }
  }, [tasks]);

  // 计算任务统计信息
  const getTaskStats = useCallback(() => {
    const total = tasks.length;
    const running = tasks.filter(t => t.status === 'running').length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const failed = tasks.filter(t => t.status === 'failed').length;
    const pending = tasks.filter(t => t.status === 'pending').length;
    const cancelled = tasks.filter(t => t.status === 'cancelled').length;
    
    return { 
      total, 
      running, 
      completed, 
      failed, 
      pending, 
      cancelled 
    };
  }, [tasks]);

  // 按状态获取任务
  const getTasksByStatus = useCallback((status) => {
    return tasks.filter(task => task.status === status);
  }, [tasks]);

  // 获取最近的任务
  const getRecentTasks = useCallback((limit = 5) => {
    return tasks
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);
  }, [tasks]);

  // 格式化任务状态显示文本
  const getStatusText = useCallback((status) => {
    const statusMap = {
      'pending': '等待中',
      'running': '执行中',
      'completed': '已完成',
      'failed': '失败',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }, []);

  // 格式化任务状态颜色
  const getStatusColor = useCallback((status) => {
    const colorMap = {
      'pending': 'warning',
      'running': 'info',
      'completed': 'success',
      'failed': 'error',
      'cancelled': 'secondary'
    };
    return colorMap[status] || 'secondary';
  }, []);

  return {
    tasks,
    loading,
    error,
    stats: getTaskStats(),
    createTask,
    startTask,
    stopTask,
    deleteTask,
    getTaskResults,
    getTasksByStatus,
    getRecentTasks,
    getStatusText,
    getStatusColor,
    refetch: fetchTasks
  };
}
