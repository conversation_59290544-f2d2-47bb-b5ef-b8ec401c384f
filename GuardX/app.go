package main

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"GuardX/internal/database"
	"GuardX/internal/models"
	"GuardX/internal/service"
)

// App struct
type App struct {
	ctx           context.Context
	serverService *service.ServerService
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// 初始化数据库
	if err := a.initDatabase(); err != nil {
		fmt.Printf("初始化数据库失败: %v\n", err)
		return
	}

	// 初始化服务
	a.serverService = service.NewServerService()

	// 初始化示例数据
	a.initSampleData()
}

// initDatabase 初始化数据库
func (a *App) initDatabase() error {
	// 获取用户数据目录
	dataDir, err := a.getDataDirectory()
	if err != nil {
		return fmt.Errorf("获取数据目录失败: %v", err)
	}

	// 确保数据目录存在
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return fmt.Errorf("创建数据目录失败: %v", err)
	}

	fmt.Printf("数据目录: %s\n", dataDir)
	fmt.Printf("数据库文件: %s\n", filepath.Join(dataDir, "guardx.db"))

	// 初始化数据库
	return database.InitDatabase(dataDir)
}

// getDataDirectory 获取数据目录
func (a *App) getDataDirectory() (string, error) {
	// 优先使用程序所在目录，便于数据管理和备份
	exePath, err := os.Executable()
	if err != nil {
		// 如果获取程序路径失败，使用用户配置目录作为备用方案
		configDir, err := os.UserConfigDir()
		if err != nil {
			return "", fmt.Errorf("获取数据目录失败: %v", err)
		}
		return filepath.Join(configDir, "GuardX"), nil
	}

	// 在程序所在目录下创建data子目录
	return filepath.Join(filepath.Dir(exePath), "data"), nil
}

// initSampleData 初始化示例数据（已禁用）
func (a *App) initSampleData() {
	// 不再自动创建示例数据，让用户手动添加服务器
	fmt.Println("数据库初始化完成，请手动添加服务器")
}

// ========== 服务器管理 API ==========

// GetServers 获取所有服务器列表
func (a *App) GetServers() []models.Server {
	servers, err := a.serverService.GetAllServers()
	if err != nil {
		fmt.Printf("获取服务器列表失败: %v\n", err)
		return []models.Server{}
	}
	return servers
}

// GetServerStats 获取服务器统计信息
func (a *App) GetServerStats() models.ServerStats {
	stats, err := a.serverService.GetServerStats()
	if err != nil {
		fmt.Printf("获取服务器统计信息失败: %v\n", err)
		return models.ServerStats{}
	}
	return *stats
}

// AddServer 添加新服务器
func (a *App) AddServer(name, ip string, port int, username, authType, password, keyFile, keyPassphrase string) (models.Server, error) {
	req := &models.CreateServerRequest{
		Name:          name,
		IP:            ip,
		Port:          port,
		Username:      username,
		AuthType:      authType,
		Password:      password,
		KeyFile:       keyFile,
		KeyPassphrase: keyPassphrase,
	}

	server, err := a.serverService.CreateServer(req)
	if err != nil {
		return models.Server{}, err
	}

	return *server, nil
}

// UpdateServer 更新服务器信息
func (a *App) UpdateServer(id int, name, ip string, port int, username, authType, password, keyFile, keyPassphrase string) error {
	req := &models.UpdateServerRequest{
		Name:          name,
		IP:            ip,
		Port:          port,
		Username:      username,
		AuthType:      authType,
		Password:      password,
		KeyFile:       keyFile,
		KeyPassphrase: keyPassphrase,
	}

	return a.serverService.UpdateServer(id, req)
}

// DeleteServer 删除服务器
func (a *App) DeleteServer(id int) error {
	return a.serverService.DeleteServer(id)
}

// ToggleServerSelection 切换服务器选择状态
func (a *App) ToggleServerSelection(id int) error {
	return a.serverService.ToggleServerSelection(id)
}

// SelectAllServers 全选/取消全选服务器
func (a *App) SelectAllServers(selected bool) error {
	return a.serverService.SelectAllServers(selected)
}

// ========== 连接测试功能 ==========

// TestConnection 测试单个服务器连接
func (a *App) TestConnection(id int) models.TestConnectionResult {
	result, err := a.serverService.TestServerConnection(id)
	if err != nil {
		fmt.Printf("测试连接失败: %v\n", err)
		return models.TestConnectionResult{
			ServerID: id,
			Success:  false,
			Message:  "测试连接时发生错误",
			Status:   "offline",
		}
	}
	return *result
}

// TestBatchConnection 批量测试连接
func (a *App) TestBatchConnection() []models.TestConnectionResult {
	results, err := a.serverService.TestBatchConnection()
	if err != nil {
		fmt.Printf("批量测试连接失败: %v\n", err)
		return []models.TestConnectionResult{}
	}
	return results
}

// ========== 模拟连接测试（用于演示） ==========



// TestServerConnection 纯粹的服务器连接测试功能
func (a *App) TestServerConnection(ip string, port int, username, authType, password, keyFile, keyPassphrase string) models.TestConnectionResult {
	// 创建连接配置
	config := &models.ConnectionConfig{
		IP:            ip,
		Port:          port,
		Username:      username,
		AuthType:      authType,
		Password:      password,
		KeyFile:       keyFile,
		KeyPassphrase: keyPassphrase,
	}

	result := a.serverService.TestConnection(config)
	return result
}
