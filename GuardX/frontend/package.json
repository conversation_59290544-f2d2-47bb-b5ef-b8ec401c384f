{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "dev:debug": "vite --debug transform", "dev:profile": "vite --profile", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:profile": "vite build --profile", "preview": "vite preview", "preview:network": "vite preview --host", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "deps:update": "npm update", "deps:audit": "npm audit", "performance:bundle": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0"}, "devDependencies": {"@types/react": "^19.1.12", "@types/react-dom": "^19.1.8", "@vitejs/plugin-react": "^5.0.1", "vite": "^7.1.3"}}