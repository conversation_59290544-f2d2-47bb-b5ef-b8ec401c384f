/* GuardX 应用主样式 - 引入统一设计系统 */
@import './styles/unified-design-system.css';
@import './styles/responsive-layout.css';

/* 应用特定样式覆盖和扩展 */

/* 任务相关样式 */
.task-name {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.task-name .name {
  font-weight: 500;
  color: var(--gray-900);
}

.task-name .description {
  font-size: var(--text-xs);
  color: var(--gray-500);
}

.task-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

/* 服务器信息样式 */
.server-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.server-results {
  display: flex;
  gap: var(--space-2);
  font-size: var(--text-xs);
}

.server-results .success {
  color: var(--success-600);
}

.server-results .warning {
  color: var(--warning-600);
}

.server-results .error {
  color: var(--error-600);
}

/* 合规率样式 */
.compliance {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

.compliance-good {
  background: var(--success-100);
  color: var(--success-700);
}

.compliance-medium {
  background: var(--warning-100);
  color: var(--warning-700);
}

.compliance-poor {
  background: var(--error-100);
  color: var(--error-700);
}

/* 结果摘要样式 */
.result-summary {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-xs);
}

.result-summary .success {
  color: var(--success-600);
}

.result-summary .warning {
  color: var(--warning-600);
}

.result-summary .error {
  color: var(--error-600);
}

/* 导出按钮样式 */
.btn-export {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-700);
}

.btn-export:hover {
  background: var(--primary-100);
  border-color: var(--primary-300);
}

/* 任务信息样式 */
.task-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.task-info .task-name {
  font-weight: 500;
  color: var(--gray-900);
  font-size: var(--text-sm);
}

.task-info .task-desc {
  font-size: var(--text-xs);
  color: var(--gray-500);
}

/* 完成时间样式 */
.completion-time {
  font-size: var(--text-xs);
  color: var(--gray-600);
}

/* 服务器数量样式 */
.server-count {
  font-weight: 500;
  color: var(--gray-700);
}

/* 状态文本样式 */
.status-text {
  font-size: var(--text-xs);
  font-weight: 500;
  margin-left: var(--space-2);
}

.status-text.enabled {
  color: var(--success-600);
}

.status-text.disabled {
  color: var(--gray-500);
}

/* 表单部分样式 */
.form-section {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-4) 0;
}



/* 任务状态信息样式 */
.task-status-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.status-value {
  font-weight: 500;
  color: var(--gray-900);
}

/* 警告文本样式 */
.warning-text {
  color: var(--warning-600);
  font-size: var(--text-sm);
  margin-top: var(--space-2);
}



/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: var(--space-12);
}

.loading-spinner {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-6);
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  font-size: var(--text-sm);
  font-weight: 500;
  box-shadow: var(--shadow-lg);
  color: var(--gray-700);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}











