import { useState, useEffect } from 'react';

/**
 * 服务器数据管理Hook - 连接后端API
 */
export function useServerData() {
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取服务器数据
  const fetchServers = async () => {
    setLoading(true);
    setError(null);

    try {
      // 调用后端API获取服务器列表
      const serverList = await window.go.main.App.GetServers();
      setServers(serverList || []);
    } catch (err) {
      setError(err.message);
      console.error('获取服务器列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 添加服务器
  const addServer = async (serverData) => {
    try {
      // 前端验证
      if (!serverData.name || !serverData.name.trim()) {
        throw new Error('服务器名称不能为空');
      }

      // 检查服务器名称是否重复
      const existingServerByName = servers.find(s => s.name.toLowerCase() === serverData.name.trim().toLowerCase());
      if (existingServerByName) {
        throw new Error(`服务器名称 "${serverData.name}" 已存在，请使用其他名称`);
      }

      // 检查IP地址和端口是否重复
      const existingServerByIP = servers.find(s => s.ip === serverData.ip.trim() && s.port === serverData.port);
      if (existingServerByIP) {
        throw new Error(`服务器 ${serverData.ip}:${serverData.port} 已存在，请检查IP地址和端口`);
      }

      if (!serverData.ip || !serverData.ip.trim()) {
        throw new Error('IP地址不能为空');
      }

      // 验证IP地址格式
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      if (!ipRegex.test(serverData.ip.trim())) {
        throw new Error('IP地址格式不正确，请输入有效的IPv4地址（如：*************）');
      }
      if (!serverData.port || serverData.port < 1 || serverData.port > 65535) {
        throw new Error('端口号必须在1-65535之间');
      }
      if (!serverData.username || !serverData.username.trim()) {
        throw new Error('用户名不能为空');
      }
      if (serverData.authType === 'password' && (!serverData.password || !serverData.password.trim())) {
        throw new Error('密码认证时密码不能为空');
      }
      if (serverData.authType === 'key' && (!serverData.keyFile || !serverData.keyFile.trim())) {
        throw new Error('密钥认证时密钥文件路径不能为空');
      }

      const newServer = await window.go.main.App.AddServer(
        serverData.name.trim(),
        serverData.ip.trim(),
        serverData.port || 22,
        serverData.username.trim() || 'root',
        serverData.authType || 'password',
        serverData.password || '',
        serverData.keyFile || '',
        serverData.keyPassphrase || ''
      );
      // 刷新服务器列表
      await fetchServers();
      return newServer;
    } catch (err) {
      const errorMessage = err.message || '添加服务器时发生未知错误';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // 更新服务器
  const updateServer = async (id, updates) => {
    try {
      // 前端验证
      if (!updates.name || !updates.name.trim()) {
        throw new Error('服务器名称不能为空');
      }

      // 检查服务器名称是否与其他服务器重复
      const existingServerByName = servers.find(s => s.id !== id && s.name.toLowerCase() === updates.name.trim().toLowerCase());
      if (existingServerByName) {
        throw new Error(`服务器名称 "${updates.name}" 已存在，请使用其他名称`);
      }

      // 检查IP地址和端口是否与其他服务器重复
      const existingServerByIP = servers.find(s => s.id !== id && s.ip === updates.ip.trim() && s.port === updates.port);
      if (existingServerByIP) {
        throw new Error(`服务器 ${updates.ip}:${updates.port} 已存在，请检查IP地址和端口`);
      }

      if (!updates.ip || !updates.ip.trim()) {
        throw new Error('IP地址不能为空');
      }

      // 验证IP地址格式
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      if (!ipRegex.test(updates.ip.trim())) {
        throw new Error('IP地址格式不正确，请输入有效的IPv4地址（如：*************）');
      }
      if (!updates.port || updates.port < 1 || updates.port > 65535) {
        throw new Error('端口号必须在1-65535之间');
      }
      if (!updates.username || !updates.username.trim()) {
        throw new Error('用户名不能为空');
      }
      if (updates.authType === 'password' && (!updates.password || !updates.password.trim())) {
        throw new Error('密码认证时密码不能为空');
      }
      if (updates.authType === 'key' && (!updates.keyFile || !updates.keyFile.trim())) {
        throw new Error('密钥认证时密钥文件路径不能为空');
      }

      await window.go.main.App.UpdateServer(
        id,
        updates.name.trim(),
        updates.ip.trim(),
        updates.port || 22,
        updates.username.trim() || 'root',
        updates.authType || 'password',
        updates.password || '',
        updates.keyFile || '',
        updates.keyPassphrase || ''
      );
      // 刷新服务器列表
      await fetchServers();
    } catch (err) {
      const errorMessage = err.message || '更新服务器时发生未知错误';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // 删除服务器
  const removeServer = async (id) => {
    try {
      await window.go.main.App.DeleteServer(id);
      // 刷新服务器列表
      await fetchServers();
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 切换服务器选择状态
  const toggleServerSelection = async (id) => {
    try {
      if (window.go && window.go.main && window.go.main.App) {
        await window.go.main.App.ToggleServerSelection(id);
        // 刷新服务器列表
        await fetchServers();
      } else {
        // 开发环境：显示错误信息
        throw new Error('后端API不可用，请确保应用正常运行');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 全选/取消全选服务器
  const selectAllServers = async (selected) => {
    try {
      if (window.go && window.go.main && window.go.main.App) {
        await window.go.main.App.SelectAllServers(selected);
        // 刷新服务器列表
        await fetchServers();
      } else {
        // 开发环境：显示错误信息
        throw new Error('后端API不可用，请确保应用正常运行');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 测试连接
  const testConnection = async (serverId) => {
    try {
      if (!serverId) {
        throw new Error('服务器ID不能为空');
      }

      const result = await window.go.main.App.TestConnection(serverId);
      // 刷新服务器列表以更新状态
      await fetchServers();
      return result;
    } catch (err) {
      const errorMessage = err.message || '测试连接时发生未知错误';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // 批量测试连接
  const testBatchConnection = async () => {
    try {
      const results = await window.go.main.App.TestBatchConnection();
      // 刷新服务器列表以更新状态
      await fetchServers();
      return results;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 获取统计信息
  const getServerStats = async () => {
    try {
      if (window.go && window.go.main && window.go.main.App) {
        return await window.go.main.App.GetServerStats();
      } else {
        // 开发环境：显示错误信息
        throw new Error('后端API不可用，请确保应用正常运行');
      }
    } catch (err) {
      setError(err.message);
      return { total: 0, selected: 0, online: 0, offline: 0 };
    }
  };

  // 计算本地统计信息（用于实时显示）
  const localStats = () => {
    const total = servers.length;
    const selected = servers.filter(s => s.selected).length;
    const online = servers.filter(s => s.status === 'online').length;
    const offline = servers.filter(s => s.status === 'offline').length;
    return { total, selected, online, offline };
  };

  useEffect(() => {
    fetchServers();
  }, []);

  return {
    servers,
    loading,
    error,
    serverStats: localStats(),
    addServer,
    updateServer,
    removeServer,
    toggleServerSelection,
    selectAllServers,
    testConnection,
    testBatchConnection,
    getServerStats,
    refetch: fetchServers
  };
}
