/* GuardX 统一设计系统 - 专业安全工具界面 */

:root {
  /* === 核心颜色系统 === */
  /* 主色调 - 专业蓝绿色 */
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-200: #a7f3d0;
  --primary-300: #6ee7b7;
  --primary-400: #34d399;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;

  /* 中性色系 - 现代深色主题 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* 语义化颜色 */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;

  /* === 字体系统 === */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-mono: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', monospace;

  /* 字体大小 - 基于16px基准 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */

  /* === 间距系统 - 基于4px网格 === */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */

  /* === 圆角系统 === */
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* === 阴影系统 === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

  /* === 过渡动画 === */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* === 层级系统 === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* === 全局重置 === */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background: var(--gray-50);
  color: var(--gray-900);
  overflow: hidden;
  height: 100vh;
  margin: 0;
  padding: 0;
}

#root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* === 应用布局 === */
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--gray-50);
}

/* 自定义标题栏样式已移除，使用Wails自带标题栏 */

/* 标题栏相关样式已移除 */

/* 工具栏 */
.app-toolbar {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  box-shadow: var(--shadow-sm);
}

.app-nav-tabs {
  display: flex;
  gap: var(--space-1);
  align-items: center;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--gray-600);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  min-height: 40px;
}

.nav-tab:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
  color: var(--gray-700);
}

.nav-tab.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-tab.active:hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
}

/* 主内容区域 */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: var(--space-6);
  gap: var(--space-6);
}

.content-panel {
  flex: 1;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

/* 状态栏 */
.app-statusbar {
  background: var(--gray-100);
  border-top: 1px solid var(--gray-200);
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-xs);
  color: var(--gray-600);
  height: 32px;
  flex-shrink: 0;
  margin-top: auto;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-500);
}

/* === 页面头部 === */
.page-header {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-6) var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

/* === 统计信息面板 === */
.stats-container {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6) var(--space-8);
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-8);
  box-shadow: var(--shadow-sm);
  min-height: 80px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  position: relative;
  flex-shrink: 0;
  min-width: 120px;
  padding: var(--space-2) 0;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: calc(-1 * var(--space-4) / 2);
  width: 1px;
  height: 24px;
  background: var(--gray-300);
  top: 50%;
  transform: translateY(-50%);
  z-index: 0;
  opacity: 0.6;
}

/* 统计图标样式已移除 */

.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stat-label {
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
}

/* 统计数值颜色 */
.stat-success { color: var(--success-500); }
.stat-warning { color: var(--warning-500); }
.stat-error { color: var(--error-500); }

/* === 统一按钮系统 === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: 8px;
  background: #f8fafc;
  color: #334155;
  font-family: var(--font-family);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  min-height: 40px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* 按钮悬停效果 */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  background: #f1f5f9;
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--gray-100);
  color: var(--gray-400);
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border-color: #2563eb;
}

.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border-color: #e2e8f0;
}

.btn-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #10b981;
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #059669;
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border-color: #f59e0b;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  border-color: #d97706;
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border-color: #dc2626;
}

/* 按钮尺寸变体 */
.btn-small {
  padding: 6px 12px;
  font-size: 12px;
  min-height: 32px;
  gap: 4px;
}

.btn-large {
  padding: 14px 24px;
  font-size: 16px;
  min-height: 48px;
  gap: 10px;
}

/* 确保所有按钮类型都继承基础样式 */
.btn-small,
.btn-large,
.compact-button,
.btn-file {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: 8px;
  background: #f8fafc;
  color: #334155;
  font-family: var(--font-family);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.btn-small:hover,
.btn-large:hover,
.compact-button:hover,
.btn-file:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  background: #f1f5f9;
}

.btn-small:active,
.btn-large:active,
.compact-button:active,
.btn-file:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 紧凑按钮 */
.compact-button {
  padding: 8px 12px;
  font-size: 13px;
  min-height: 36px;
  gap: 6px;
}



/* === 表格系统 === */
.table-container {
  flex: 1;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-sm);
}

.table-content {
  flex: 1;
  overflow: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

/* 状态列居中对齐 */
.table td:nth-child(4),
.table th:nth-child(4) {
  text-align: center;
}

/* 复选框列居中对齐 */
.table td:first-child,
.table th:first-child {
  text-align: center;
}

/* 状态列居中对齐 */
.table td:nth-child(5),
.table th:nth-child(5) {
  text-align: center;
}

/* 操作列居中对齐 */
.table td:last-child,
.table th:last-child {
  text-align: center;
}

/* 复选框样式 */
.table input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-500);
}

.table th {
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-300);
  font-weight: 600;
  color: var(--gray-700);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  background: white;
  color: var(--gray-900);
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background: var(--gray-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* === 操作按钮组 === */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}



/* 旧的按钮样式已移除，统一使用新的按钮系统 */

/* === 状态指示器 === */
.status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  text-align: center;
}

.status-online {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-offline {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}



.status-pending {
  background: var(--gray-50);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.status-running {
  background: var(--info-50);
  color: var(--info-700);
  border: 1px solid var(--info-200);
}

.status-completed {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.status-failed {
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

.status-cancelled {
  background: var(--gray-50);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

/* === 表单系统 === */
.form-group {
  margin-bottom: var(--space-4);
}

/* 基础输入框样式 */
input[type="text"],
input[type="password"],
input[type="number"],
input[type="email"],
input[type="url"],
textarea,
select {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-base);
  font-family: var(--font-family);
  font-size: var(--text-sm);
  color: var(--gray-900);
  background: white;
  transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="text"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="email"]:disabled,
input[type="url"]:disabled,
textarea:disabled,
select:disabled {
  background: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

/* 标签样式 */
label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--gray-700);
  font-size: var(--text-sm);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--gray-700);
  font-size: var(--text-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--gray-900);
  font-family: var(--font-family);
  font-size: var(--text-sm);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* === 单选按钮组 === */
.radio-group {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-2);
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-weight: normal;
  margin-bottom: 0;
}

.radio-group input[type="radio"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

/* === 文件输入组 === */
.file-input {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.file-input input {
  flex: 1;
}

/* 文件按钮 - 继承基础按钮样式 */
.btn-file {
  padding: 8px 12px;
  min-height: 36px;
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* === 表单分组样式 === */
.form-section {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--gray-900);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.server-summary {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.server-actions {
  margin-bottom: var(--space-3);
}

/* === 服务器列表样式 === */
.server-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-base);
  padding: var(--space-2);
}

.server-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.server-item:hover:not(.disabled) {
  background: var(--gray-50);
}

.server-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.server-item input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

.server-info {
  flex: 1;
}

.server-name {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 500;
  color: var(--gray-900);
}

.server-ip {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin-top: var(--space-1);
}

.server-status {
  font-size: var(--text-xs);
}

/* === 配置信息样式 === */
.current-config {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-base);
  padding: var(--space-4);
}

.config-info {
  margin-bottom: var(--space-3);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.config-name {
  font-weight: 600;
  color: var(--gray-900);
}

.config-meta {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.item-count {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
  font-size: var(--text-xs);
  font-weight: 500;
}

.config-desc {
  color: var(--gray-700);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.config-note {
  font-size: var(--text-xs);
  color: var(--gray-500);
  font-style: italic;
}

/* === 预览面板样式 === */
.preview-panel {
  background: var(--primary-50);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-base);
  padding: var(--space-4);
}

.preview-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-item .label {
  font-weight: 500;
  color: var(--gray-700);
}

.preview-item .value {
  color: var(--gray-900);
}

/* === 通用对话框内容样式 === */
.dialog-section {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.dialog-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.dialog-section h4 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--gray-900);
}

.info-panel {
  background: var(--gray-50);
  border-radius: var(--radius-base);
  padding: var(--space-4);
}

.info-panel > div {
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.info-panel > div:last-child {
  margin-bottom: 0;
}

.config-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.config-item label {
  font-weight: 500;
  color: var(--gray-700);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: normal;
  cursor: pointer;
}

/* === 模态框系统 === */
/* === 现代化对话框系统 === */

/* 对话框遮罩层 */
.dialog-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(12px) saturate(180%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  padding: clamp(16px, 4vw, 32px);
  animation: overlayFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) saturate(100%);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px) saturate(180%);
  }
}

/* 对话框主体 */
.dialog {
  background: #ffffff;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.9) inset;
  max-height: 90vh;
  overflow: hidden;
  z-index: var(--z-modal);
  animation: dialogSlideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 对话框尺寸变体 */
.dialog-small {
  width: 400px;
  max-width: 90vw;
}

.dialog-medium {
  width: 500px;
  max-width: 90vw;
}

.dialog-large {
  width: 700px;
  max-width: 90vw;
}

/* 警告文字样式 */
.text-warning {
  color: var(--warning-600);
  font-size: 14px;
  margin-top: 8px;
  font-weight: 500;
}

.dialog-fullscreen {
  width: 95vw;
  height: 95vh;
  max-width: none;
  max-height: none;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.96) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(248, 250, 252, 0.5);
  flex-shrink: 0;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  letter-spacing: -0.025em;
}

.dialog-close {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-close:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  transform: scale(1.05);
}

.dialog-close:active {
  transform: scale(0.95);
}

/* 小屏幕响应式优化 */
@media (max-width: 640px) {
  .modal {
    width: 95vw;
    min-width: 280px;
    max-height: 90vh;
    border-radius: 12px;
    margin: 0.5rem;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
    max-height: calc(90vh - 140px);
  }

  .modal-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .modal-actions {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }

  .modal-actions .btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-6) var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 对话框内容区域 */
.dialog-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* 对话框消息样式 */
.dialog-message {
  text-align: center;
}

.dialog-message p {
  font-size: 16px;
  line-height: 1.6;
  color: #475569;
  margin: 0;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .dialog-small,
  .dialog-medium,
  .dialog-large {
    width: 95vw;
    margin: 0;
  }

  .dialog-header {
    padding: 16px 20px;
  }

  .dialog-content {
    padding: 20px;
  }

  .dialog-footer {
    padding: 16px 20px;
    flex-direction: column;
  }

  .dialog-actions {
    width: 100%;
    flex-direction: column-reverse;
    gap: 8px;
  }

  .dialog-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-height: 600px) {
  .dialog {
    max-height: 95vh;
  }

  .dialog-content {
    padding: 16px 24px;
  }
}

/* 对话框底部 */
.dialog-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(248, 250, 252, 0.3);
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: center;
}

/* 对话框变体样式 */
.dialog-success .dialog-header {
  background: rgba(34, 197, 94, 0.1);
  border-bottom-color: rgba(34, 197, 94, 0.2);
}

.dialog-warning .dialog-header {
  background: rgba(245, 158, 11, 0.1);
  border-bottom-color: rgba(245, 158, 11, 0.2);
}

.dialog-error .dialog-header {
  background: rgba(239, 68, 68, 0.1);
  border-bottom-color: rgba(239, 68, 68, 0.2);
}

.dialog-info .dialog-header {
  background: rgba(59, 130, 246, 0.1);
  border-bottom-color: rgba(59, 130, 246, 0.2);
}

/* 旧的弹窗样式已移除，使用新的对话框系统 */

/* 旧的弹窗按钮样式已移除 */

/* === 进度条 === */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
}

.progress-text {
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--gray-600);
  min-width: 40px;
  text-align: right;
}

/* === 徽章和标签 === */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background: var(--success-100);
  color: var(--success-700);
}

.badge-warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.badge-error {
  background: var(--error-100);
  color: var(--error-700);
}

/* === 级别指示器 === */
.level {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.level-high {
  background: var(--error-100);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

.level-medium {
  background: var(--warning-100);
  color: var(--warning-700);
  border: 1px solid var(--warning-200);
}

.level-low {
  background: var(--success-100);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

/* === 开关组件 === */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: var(--radius-full);
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .slider {
  background-color: var(--primary-500);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* === 滚动条样式 === */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) var(--gray-100);
}

/* === 工具类 === */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.transition { transition: all var(--transition-fast); }

/* 图标样式系统已移除 */

/* 按钮内图标样式 */
.btn svg,
.btn-small svg,
.compact-button svg {
  flex-shrink: 0;
}

/* 确保按钮内容正确对齐 */
.btn span,
.btn-small span,
.compact-button span {
  line-height: 1;
}

/* 标题内图标样式 */
.page-title {
  display: flex;
  align-items: center;
}



/* 导航标签样式 */
.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* 应用标题样式 */
.app-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* 重复的状态样式已移除 */

/* 结果摘要样式 */
.result-summary {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.result-summary span {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* 颜色工具类 */
.text-green-500 { color: var(--success-500); }
.text-red-500 { color: var(--error-500); }
.text-yellow-500 { color: var(--warning-500); }
.text-blue-500 { color: var(--info-500); }
.text-gray-700 { color: var(--gray-700); }

/* === 弹窗消息样式优化 === */
.modal-message {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-700);
  margin: 0;
  padding: var(--space-2) 0;
}

/* 弹窗图标样式已移除 */

/* 弹窗按钮间距优化 */
.modal-actions {
  gap: var(--space-3);
}

/* 弹窗关闭按钮悬停效果 */
.modal-close:hover {
  background: var(--gray-100);
  border-radius: var(--radius-base);
}

/* 弹窗标题图标样式已移除 */

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .modal-close {
    min-width: 44px;
    min-height: 44px;
  }

  .modal-actions .btn {
    min-height: 44px;
    touch-action: manipulation;
  }
}

/* === 加载指示器 === */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  border: 2px solid transparent !important;
  border-top: 2px solid currentColor !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-right: 4px !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 按钮中的spinner样式 */
.btn .spinner,
.btn-info .spinner {
  margin-right: 4px !important;
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 测试中状态按钮样式 */
.btn-info {
  background: #6b7280 !important;
  color: white !important;
  border: 1px solid #6b7280 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 6px 12px !important;
  font-size: 12px !important;
  min-height: 32px !important;
  gap: 4px !important;
  border-radius: 8px !important;
  font-family: var(--font-family) !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  text-decoration: none !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  position: relative !important;
  overflow: hidden !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.btn-info:hover:not(:disabled) {
  background: #4b5563 !important;
  border-color: #4b5563 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15) !important;
}

.btn-info:disabled {
  background: #9ca3af !important;
  border-color: #9ca3af !important;
  cursor: not-allowed !important;
  color: white !important;
  transform: none !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  opacity: 1 !important;
  visibility: visible !important;
}
