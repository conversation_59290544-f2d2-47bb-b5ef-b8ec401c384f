/* 响应式窗口布局优化 (最小960x640) */

/* 应用容器响应式尺寸 */
.app-container {
  width: 100vw !important;
  height: 100vh !important;
  min-width: 960px !important;
  min-height: 640px !important;
  overflow: hidden !important;
  position: relative;
}

/* 标题栏优化 */
.custom-titlebar {
  height: 32px !important;
  min-height: 32px !important;
  flex-shrink: 0;
  -webkit-app-region: drag !important;
}

.titlebar-content {
  -webkit-app-region: drag !important;
}

.titlebar-controls {
  -webkit-app-region: no-drag !important;
}

.titlebar-button {
  -webkit-app-region: no-drag !important;
}

/* 工具栏优化 */
.app-toolbar {
  min-height: 56px !important;
  max-height: 56px !important;
  padding: var(--space-3) var(--space-4) !important;
  flex-shrink: 0;
}

.app-nav-tabs {
  gap: var(--space-2) !important;
}

.nav-tab {
  padding: var(--space-2) var(--space-3) !important;
  font-size: var(--text-sm) !important;
  min-height: 36px !important;
}

/* 主内容区域优化 */
.app-main {
  height: calc(100vh - 32px - 56px - 32px) !important; /* 总高度 - 标题栏 - 工具栏 - 状态栏 */
  max-height: calc(100vh - 32px - 56px - 32px) !important;
  padding: var(--space-4) !important;
  gap: var(--space-4) !important;
  overflow: hidden;
  flex-shrink: 1;
}

/* 页面头部优化 */
.page-header {
  padding: var(--space-3) var(--space-4) !important;
  min-height: 48px !important;
  max-height: 48px !important;
  flex-shrink: 0;
}

.page-title {
  font-size: var(--text-lg) !important;
  margin: 0 !important;
}

.page-actions {
  gap: var(--space-2) !important;
}

/* 统计信息面板优化 */
.stats-container {
  padding: var(--space-3) var(--space-4) !important;
  margin-bottom: var(--space-3) !important;
  gap: var(--space-4) !important;
  flex-shrink: 0;
}

.stat-item {
  gap: var(--space-2) !important;
}

.stat-value {
  font-size: var(--text-lg) !important;
}

.stat-label {
  font-size: var(--text-xs) !important;
}

/* 表格容器优化 */
.table-container {
  flex: 1 !important;
  height: calc(100vh - 200px) !important;
  min-height: 200px !important;
  max-height: calc(100vh - 200px) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

.table-content {
  flex: 1 !important;
  overflow: auto !important;
  height: 100% !important;
  min-height: 0 !important;
}

/* 表格样式优化 */
.table {
  font-size: var(--text-sm) !important;
  width: 100% !important;
}

.table th {
  padding: var(--space-2) var(--space-3) !important;
  font-size: var(--text-xs) !important;
  height: 32px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.table td {
  padding: var(--space-2) var(--space-3) !important;
  height: 36px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  vertical-align: middle !important;
}

/* 操作按钮优化 */
.action-buttons {
  gap: var(--space-1) !important;
  display: flex !important;
  flex-wrap: nowrap !important;
}

.btn-small,
.compact-button {
  padding: var(--space-1) var(--space-2) !important;
  font-size: var(--text-xs) !important;
  min-height: 28px !important;
  min-width: 50px !important;
  white-space: nowrap !important;
}

/* 状态指示器优化 */
.status {
  padding: var(--space-1) var(--space-2) !important;
  font-size: var(--text-xs) !important;
  white-space: nowrap !important;
}

.task-status {
  font-size: var(--text-xs) !important;
  padding: var(--space-1) var(--space-2) !important;
}

/* 进度条优化 */
.progress-container {
  width: 100% !important;
  max-width: 60px !important;
}

.progress-bar {
  height: 6px !important;
}

.progress-text {
  font-size: var(--text-xs) !important;
  min-width: 30px !important;
}

/* 任务名称优化 */
.task-name .name {
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 160px !important;
}

.task-name .description {
  font-size: var(--text-xs) !important;
  color: var(--gray-500) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 160px !important;
}

/* 服务器信息优化 */
.server-info {
  font-size: var(--text-sm) !important;
}

.server-results {
  font-size: var(--text-xs) !important;
  gap: var(--space-1) !important;
}

/* 状态栏优化 */
.app-statusbar {
  height: 32px !important;
  min-height: 32px !important;
  max-height: 32px !important;
  padding: var(--space-2) var(--space-4) !important;
  font-size: var(--text-xs) !important;
  flex-shrink: 0;
}

/* 模态框优化已移至unified-design-system.css */

/* 表单优化 */
.form-group {
  margin-bottom: var(--space-3) !important;
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--space-2) !important;
  font-size: var(--text-sm) !important;
}

/* 滚动条优化 */
.table-content::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

.table-content::-webkit-scrollbar-track {
  background: var(--gray-100) !important;
}

.table-content::-webkit-scrollbar-thumb {
  background: var(--gray-300) !important;
  border-radius: var(--radius-base) !important;
}

.table-content::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400) !important;
}

/* 模态框滚动条样式已移至unified-design-system.css */

/* 紧凑布局工具类 */
.compact-layout {
  padding: var(--space-2) !important;
  gap: var(--space-2) !important;
}

.compact-text {
  font-size: var(--text-sm) !important;
  line-height: 1.3 !important;
}

/* 确保内容不会溢出 */
.content-panel {
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  min-height: 0 !important;
}

/* 任务计数样式 */
.task-count {
  font-size: var(--text-sm) !important;
  color: var(--gray-600) !important;
  font-weight: 500 !important;
}

/* 响应式设计 - 根据窗口大小调整 */

/* 中等窗口 (1200px+) */
@media (min-width: 1200px) {
  .table-container {
    height: calc(100vh - 180px) !important;
    max-height: calc(100vh - 180px) !important;
  }

  .table th,
  .table td {
    padding: var(--space-3) var(--space-4) !important;
  }

  .btn-small,
  .compact-button {
    min-width: 60px !important;
    padding: var(--space-2) var(--space-3) !important;
  }

  .task-name .name,
  .task-name .description {
    max-width: 200px !important;
  }
}

/* 大窗口 (1400px+) */
@media (min-width: 1400px) {
  .table-container {
    height: calc(100vh - 160px) !important;
    max-height: calc(100vh - 160px) !important;
  }

  .stats-container {
    gap: var(--space-6) !important;
  }

  .page-header {
    padding: var(--space-4) var(--space-6) !important;
  }

  .table th,
  .table td {
    padding: var(--space-4) var(--space-6) !important;
  }

  .task-name .name,
  .task-name .description {
    max-width: 250px !important;
  }

  .action-buttons {
    gap: var(--space-2) !important;
  }
}

/* 超大窗口 (1600px+) */
@media (min-width: 1600px) {
  .app-main {
    padding: var(--space-6) !important;
    gap: var(--space-6) !important;
  }

  .stats-container {
    padding: var(--space-4) var(--space-8) !important;
    gap: var(--space-8) !important;
  }

  .page-title {
    font-size: var(--text-xl) !important;
  }

  .table {
    font-size: var(--text-base) !important;
  }

  .table th {
    font-size: var(--text-sm) !important;
    height: 40px !important;
  }

  .table td {
    height: 44px !important;
  }

  .task-name .name,
  .task-name .description {
    max-width: 300px !important;
  }
}

/* 最小窗口 (960px-1199px) - 保持紧凑布局 */
@media (max-width: 1199px) {
  .stats-container {
    gap: var(--space-3) !important;
  }

  .stat-item {
    gap: var(--space-2) !important;
  }

  .table th,
  .table td {
    padding: var(--space-2) var(--space-3) !important;
  }

  .btn-small,
  .compact-button {
    min-width: 50px !important;
    font-size: var(--text-xs) !important;
  }

  .task-name .name,
  .task-name .description {
    max-width: 140px !important;
  }
}
