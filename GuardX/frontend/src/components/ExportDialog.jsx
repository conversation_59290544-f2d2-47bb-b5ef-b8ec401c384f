import React, { useState } from 'react';

const ExportDialog = ({ isOpen, onClose, task, onExport }) => {
  const [exportConfig, setExportConfig] = useState({
    reportType: 'complete',
    format: 'pdf',
    includeResults: true,
    includeDetails: true,
    includeSuggestions: true,
    includeLogs: false,
    savePath: '/home/<USER>/'
  });

  if (!isOpen) return null;

  const handleConfigChange = (key, value) => {
    setExportConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleExport = () => {
    onExport(task, exportConfig);
    onClose();
  };

  return (
    <div className="dialog-overlay" onClick={onClose}>
      <div className="dialog dialog-large" onClick={(e) => e.stopPropagation()}>
        <div className="dialog-header" style={{ justifyContent: 'center' }}>
          <h2 className="dialog-title">导出报告</h2>
        </div>

        <div className="dialog-content">
          <div className="dialog-section">
            <h4>任务信息</h4>
            <div className="info-panel">
              <div><strong>任务名称:</strong> {task?.name}</div>
              <div><strong>完成时间:</strong> {task?.completedAt ? new Date(task.completedAt).toLocaleString('zh-CN') : ''}</div>
              <div><strong>服务器数量:</strong> {task?.totalServers}台</div>
              <div><strong>合规率:</strong> {task?.compliance}%</div>
            </div>
          </div>

          <div className="dialog-section">
            <h4>导出配置</h4>
            <div className="config-grid">
              <div className="config-item">
                <label>报告类型:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="complete"
                      checked={exportConfig.reportType === 'complete'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    完整报告
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="issues"
                      checked={exportConfig.reportType === 'issues'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    问题报告
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="summary"
                      checked={exportConfig.reportType === 'summary'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    摘要报告
                  </label>
                </div>
              </div>

              <div className="config-item">
                <label>导出格式:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="pdf"
                      checked={exportConfig.format === 'pdf'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    PDF
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="html"
                      checked={exportConfig.format === 'html'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    HTML
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="docx"
                      checked={exportConfig.format === 'docx'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    Word
                  </label>
                </div>
              </div>

              <div className="config-item">
                <label>包含内容:</label>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeResults}
                      onChange={(e) => handleConfigChange('includeResults', e.target.checked)}
                    />
                    检查结果
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeDetails}
                      onChange={(e) => handleConfigChange('includeDetails', e.target.checked)}
                    />
                    问题详情
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeSuggestions}
                      onChange={(e) => handleConfigChange('includeSuggestions', e.target.checked)}
                    />
                    修复建议
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeLogs}
                      onChange={(e) => handleConfigChange('includeLogs', e.target.checked)}
                    />
                    执行日志
                  </label>
                </div>
              </div>

              <div className="config-item">
                <label>保存位置:</label>
                <div className="file-input">
                  <input
                    type="text"
                    value={exportConfig.savePath}
                    onChange={(e) => handleConfigChange('savePath', e.target.value)}
                    placeholder="/home/<USER>/"
                  />
                  <button className="btn btn-secondary btn-small">选择</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="dialog-footer" style={{ justifyContent: 'center', gap: '16px' }}>
          <button className="btn btn-secondary" onClick={onClose} style={{ minWidth: '80px' }}>
            取消
          </button>
          <button className="btn btn-primary" onClick={handleExport} style={{ minWidth: '100px' }}>
            开始导出
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportDialog;