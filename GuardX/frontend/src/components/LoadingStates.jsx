import React, { memo } from 'react';

/**
 * 骨架屏加载组件 - 提升用户体验
 */
export const SkeletonLoader = memo(({ rows = 5, height = 60 }) => (
  <div className="skeleton-container">
    {Array.from({ length: rows }, (_, index) => (
      <div key={index} className="skeleton-row" style={{ height }}>
        <div className="skeleton-item skeleton-avatar"></div>
        <div className="skeleton-item skeleton-text skeleton-text-long"></div>
        <div className="skeleton-item skeleton-text skeleton-text-short"></div>
        <div className="skeleton-item skeleton-button"></div>
      </div>
    ))}
  </div>
));

SkeletonLoader.displayName = 'SkeletonLoader';

/**
 * 表格骨架屏
 */
export const TableSkeleton = memo(({ columns = 5, rows = 8 }) => (
  <div className="table-skeleton">
    <div className="skeleton-table-header">
      {Array.from({ length: columns }, (_, index) => (
        <div key={index} className="skeleton-header-cell"></div>
      ))}
    </div>
    <div className="skeleton-table-body">
      {Array.from({ length: rows }, (_, rowIndex) => (
        <div key={rowIndex} className="skeleton-table-row">
          {Array.from({ length: columns }, (_, colIndex) => (
            <div key={colIndex} className="skeleton-table-cell"></div>
          ))}
        </div>
      ))}
    </div>
  </div>
));

TableSkeleton.displayName = 'TableSkeleton';

/**
 * 卡片骨架屏
 */
export const CardSkeleton = memo(({ count = 3 }) => (
  <div className="card-skeleton-container">
    {Array.from({ length: count }, (_, index) => (
      <div key={index} className="card-skeleton">
        <div className="skeleton-card-header">
          <div className="skeleton-item skeleton-avatar"></div>
          <div className="skeleton-item skeleton-text skeleton-text-medium"></div>
        </div>
        <div className="skeleton-card-body">
          <div className="skeleton-item skeleton-text skeleton-text-long"></div>
          <div className="skeleton-item skeleton-text skeleton-text-medium"></div>
          <div className="skeleton-item skeleton-text skeleton-text-short"></div>
        </div>
        <div className="skeleton-card-footer">
          <div className="skeleton-item skeleton-button"></div>
          <div className="skeleton-item skeleton-button"></div>
        </div>
      </div>
    ))}
  </div>
));

CardSkeleton.displayName = 'CardSkeleton';

/**
 * 进度条组件
 */
export const ProgressBar = memo(({ 
  progress = 0, 
  showPercentage = true, 
  color = '#4CAF50',
  height = 8,
  animated = true 
}) => (
  <div className="progress-container">
    <div 
      className="progress-bar" 
      style={{ height }}
    >
      <div 
        className={`progress-fill ${animated ? 'animated' : ''}`}
        style={{ 
          width: `${Math.min(100, Math.max(0, progress))}%`,
          backgroundColor: color 
        }}
      ></div>
    </div>
    {showPercentage && (
      <span className="progress-text">
        {Math.round(progress)}%
      </span>
    )}
  </div>
));

ProgressBar.displayName = 'ProgressBar';

/**
 * 加载旋转器
 */
export const Spinner = memo(({ 
  size = 'medium', 
  color = '#667eea',
  text = '加载中...' 
}) => {
  const sizeClass = `spinner-${size}`;
  
  return (
    <div className="spinner-container">
      <div 
        className={`spinner ${sizeClass}`}
        style={{ borderTopColor: color }}
      ></div>
      {text && <span className="spinner-text">{text}</span>}
    </div>
  );
});

Spinner.displayName = 'Spinner';

/**
 * 脉冲加载效果
 */
export const PulseLoader = memo(({ count = 3, color = '#667eea' }) => (
  <div className="pulse-loader">
    {Array.from({ length: count }, (_, index) => (
      <div 
        key={index}
        className="pulse-dot"
        style={{ 
          backgroundColor: color,
          animationDelay: `${index * 0.2}s`
        }}
      ></div>
    ))}
  </div>
));

PulseLoader.displayName = 'PulseLoader';

/**
 * 波浪加载效果
 */
export const WaveLoader = memo(({ color = '#667eea' }) => (
  <div className="wave-loader">
    {Array.from({ length: 5 }, (_, index) => (
      <div 
        key={index}
        className="wave-bar"
        style={{ 
          backgroundColor: color,
          animationDelay: `${index * 0.1}s`
        }}
      ></div>
    ))}
  </div>
));

WaveLoader.displayName = 'WaveLoader';
