import React from 'react';
import {
  // 服务器和系统相关
  HiServer,
  HiCog<PERSON><PERSON><PERSON>h,
  HiClipboardDocumentList,
  HiDocumentText,
  HiShieldCheck,
  
  // 操作相关
  HiPlus,
  HiPencil,
  HiTrash,
  HiPlay,
  HiStop,
  HiArrowPath,
  HiEye,
  HiLink,
  
  // 状态相关
  HiCheckCircle,
  HiXCircle,
  HiExclamationTriangle,
  HiClock,
  HiMinus,
  HiSquare2Stack,
  HiXMark,
  
  // 文件和导出相关
  HiFolder,
  HiDocumentArrowDown,
  HiArchiveBox,
  
  // 统计和图表相关
  HiChartBar,
  HiSignal,
  
  // 网络和连接相关
  HiGlobeAlt,
  HiWifi,
  
  // 安全相关
  HiLockClosed,
  HiKey,
  HiFingerPrint,
  
  // 窗口控制
  HiMinus as WindowMinimize,
  HiStop as WindowMaximize,
  HiXMark as WindowClose
} from 'react-icons/hi2';

// 图标组件映射
export const Icons = {
  // 主要功能模块图标
  Server: HiServer,
  Settings: HiCog6Tooth,
  Tasks: HiClipboardDocumentList,
  Report: HiDocumentText,
  Shield: HiShieldCheck,
  
  // 操作图标
  Add: HiPlus,
  Edit: HiPencil,
  Delete: HiTrash,
  Start: HiPlay,
  Stop: HiStop,
  Refresh: HiArrowPath,
  View: HiEye,
  Link: HiLink,
  
  // 状态图标
  Success: HiCheckCircle,
  Error: HiXCircle,
  Warning: HiExclamationTriangle,
  Pending: HiClock,
  Close: HiXMark,
  
  // 文件操作图标
  Folder: HiFolder,
  Export: HiDocumentArrowDown,
  Save: HiArchiveBox,
  
  // 统计图标
  Chart: HiChartBar,
  Signal: HiSignal,
  
  // 网络图标
  Network: HiGlobeAlt,
  Wifi: HiWifi,
  
  // 安全图标
  Lock: HiLockClosed,
  Key: HiKey,
  Fingerprint: HiFingerPrint,
  
  // 窗口控制图标
  WindowMinimize,
  WindowMaximize,
  WindowClose
};

// 状态图标组件
export const StatusIcon = ({ status, className = "", size = 20 }) => {
  const iconMap = {
    online: { Icon: HiCheckCircle, color: 'text-green-500' },
    offline: { Icon: HiXCircle, color: 'text-red-500' },
    pending: { Icon: HiClock, color: 'text-gray-500' },
    running: { Icon: HiArrowPath, color: 'text-blue-500' },
    completed: { Icon: HiCheckCircle, color: 'text-green-500' },
    failed: { Icon: HiXCircle, color: 'text-red-500' },
    cancelled: { Icon: HiStop, color: 'text-gray-500' }
  };

  const { Icon, color } = iconMap[status] || { Icon: HiXCircle, color: 'text-gray-400' };

  return (
    <Icon
      className={`${color} ${className}`}
      size={size}
    />
  );
};

// 风险级别图标组件
export const RiskLevelIcon = ({ level, className = "", size = 16 }) => {
  const levelMap = {
    '高危': { Icon: HiExclamationTriangle, color: 'text-red-500' },
    '中危': { Icon: HiExclamationTriangle, color: 'text-yellow-500' },
    '低危': { Icon: HiCheckCircle, color: 'text-green-500' }
  };

  const { Icon, color } = levelMap[level] || { Icon: HiCheckCircle, color: 'text-gray-400' };
  
  return (
    <Icon 
      className={`${color} ${className}`} 
      size={size}
    />
  );
};

// 通用图标组件
export const Icon = ({ name, className = "", size = 20, color = "" }) => {
  const IconComponent = Icons[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }
  
  return (
    <IconComponent 
      className={`${color} ${className}`} 
      size={size}
    />
  );
};

export default Icons;
