import React, { useState } from 'react';
import { MessageModal, ConfirmModal } from './Modal';

const CheckConfig = () => {
  const [checkItems, setCheckItems] = useState([]);

  const categories = [];

  const [showMessage, setShowMessage] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const getLevelColor = (level) => {
    switch (level) {
      case '高危': return 'high';
      case '中危': return 'medium';
      case '低危': return 'low';
      default: return 'low';
    }
  };

  const toggleItem = (id) => {
    setCheckItems(checkItems.map(item => 
      item.id === id ? { ...item, enabled: !item.enabled } : item
    ));
  };

  const enableAll = () => {
    setCheckItems(checkItems.map(item => ({ ...item, enabled: true })));
  };

  const disableAll = () => {
    setCheckItems(checkItems.map(item => ({ ...item, enabled: false })));
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const saveConfig = () => {
    showMessageModal('功能开发中', '保存配置功能正在开发中，请等待后续版本', 'info');
  };

  const resetDefault = () => {
    setShowConfirm(true);
  };

  const handleResetConfirm = () => {
    // 重置逻辑 - 暂时显示提示
    showMessageModal('功能开发中', '重置功能正在开发中，请等待后续版本', 'info');
  };

  const getStats = () => {
    const enabled = checkItems.filter(item => item.enabled).length;
    const high = checkItems.filter(item => item.enabled && item.level === '高危').length;
    const medium = checkItems.filter(item => item.enabled && item.level === '中危').length;
    const low = checkItems.filter(item => item.enabled && item.level === '低危').length;
    
    return { enabled, high, medium, low };
  };

  const stats = getStats();

  return (
    <div className="check-config">
      <div className="page-header">
        <h2 className="page-title">
          核查配置
        </h2>
        <div className="page-actions">
          <button className="btn btn-primary" onClick={saveConfig}>
            保存配置
          </button>
          <button className="btn btn-secondary" onClick={resetDefault}>
            重置默认
          </button>
        </div>
      </div>

      <div className="stats-container">
        <div className="stat-item">
          <div className="stat-content">
            <div className="stat-label">已启用</div>
            <div className="stat-value">{stats.enabled}项</div>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-content">
            <div className="stat-label">高危</div>
            <div className="stat-value stat-error">{stats.high}项</div>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-content">
            <div className="stat-label">中危</div>
            <div className="stat-value stat-warning">{stats.medium}项</div>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-content">
            <div className="stat-label">低危</div>
            <div className="stat-value stat-success">{stats.low}项</div>
          </div>
        </div>
        <div className="page-actions">
          <button className="btn btn-success" onClick={enableAll}>
            全部启用
          </button>
          <button className="btn btn-danger" onClick={disableAll}>
            全部禁用
          </button>
        </div>
      </div>

      <div className="table-container">
        <div className="table-content">
          <table className="table">
          <thead>
            <tr>
              <th width="80">分类</th>
              <th width="200">检查项名称</th>
              <th width="80">级别</th>
              <th width="80">状态</th>
              <th>描述</th>
            </tr>
          </thead>
          <tbody>
            {categories.map(category => (
              <React.Fragment key={category}>
                {checkItems
                  .filter(item => item.category === category)
                  .map(item => (
                    <tr key={item.id}>
                      <td>{item.category}</td>
                      <td>{item.name}</td>
                      <td>
                        <span className={`level level-${getLevelColor(item.level)}`}>
                          {item.level}
                        </span>
                      </td>
                      <td>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={item.enabled}
                            onChange={() => toggleItem(item.id)}
                          />
                          <span className="slider"></span>
                        </label>
                        <span className={`status-text ${item.enabled ? 'enabled' : 'disabled'}`}>
                          {item.enabled ? '启用' : '禁用'}
                        </span>
                      </td>
                      <td>{item.description}</td>
                    </tr>
                  ))}
              </React.Fragment>
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />

      {/* 确认弹窗 */}
      <ConfirmModal
        isOpen={showConfirm}
        onClose={() => setShowConfirm(false)}
        onConfirm={handleResetConfirm}
        title="确认重置"
        message="确定要重置为默认配置吗？此操作将覆盖当前所有配置。"
        type="warning"
      />
    </div>
  );
};

export default CheckConfig;
