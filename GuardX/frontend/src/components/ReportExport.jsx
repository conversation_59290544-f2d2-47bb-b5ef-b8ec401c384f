import React, { useState } from 'react';
import { MessageModal } from './Modal';
import ExportDialog from './ExportDialog';

const ReportExport = () => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // 已完成的任务列表（用于生成报告）
  const [completedTasks, setCompletedTasks] = useState([]);

  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  // 状态图标函数已移除

  const getStatusText = (status) => {
    switch (status) {
      case 'normal': return '正常';
      case 'warning': return '警告';
      case 'error': return '异常';
      default: return '未知';
    }
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const handleExportTask = (task) => {
    setSelectedTask(task);
    setShowExportDialog(true);
  };

  const handleExport = (task, config) => {
    const reportMessage = `正在导出报告...\n任务: ${task.name}\n类型: ${getReportTypeText(config.reportType)}\n格式: ${config.format.toUpperCase()}\n保存位置: ${config.savePath}`;
    showMessageModal('导出报告', reportMessage, 'info');
  };

  const refreshTasks = () => {
    showMessageModal('刷新任务', '正在刷新已完成任务列表...', 'info');
  };

  const getReportTypeText = (type) => {
    switch (type) {
      case 'complete': return '完整报告';
      case 'issues': return '问题报告';
      case 'summary': return '摘要报告';
      default: return '完整报告';
    }
  };

  const totalTasks = completedTasks.length;

  return (
    <div className="report-export">
      <div className="page-header">
        <h2 className="page-title">
          报告导出
        </h2>
        <div className="page-actions">
          <span className="task-count">共 {totalTasks} 个已完成任务</span>
          <button className="btn btn-primary" onClick={refreshTasks}>
            刷新任务
          </button>
        </div>
      </div>

      <div className="table-container">
        <div className="table-content">
          <table className="table">
          <thead>
            <tr>
              <th width="300">任务名称</th>
              <th width="120">完成时间</th>
              <th width="80">服务器</th>
              <th width="80">合规率</th>
              <th width="120">检查结果</th>
              <th width="100">操作</th>
            </tr>
          </thead>
          <tbody>
            {completedTasks.map(task => (
              <tr key={task.id}>
                <td>
                  <div className="task-info">
                    <div className="task-name" title={task.name}>{task.name}</div>
                    <div className="task-desc" title={task.description}>{task.description}</div>
                  </div>
                </td>
                <td>
                  <div className="completion-time">
                    {new Date(task.completedAt).toLocaleString('zh-CN')}
                  </div>
                </td>
                <td>
                  <span className="server-count">{task.totalServers}台</span>
                </td>
                <td>
                  <span className={`compliance compliance-${task.compliance >= 80 ? 'good' : task.compliance >= 60 ? 'medium' : 'poor'}`}>
                    {task.compliance}%
                  </span>
                </td>
                <td>
                  <div className="result-summary">
                    <span className="success">
                      成功: {task.successCount}
                    </span>
                    <span className="warning">
                      警告: {task.warningCount}
                    </span>
                    <span className="error">
                      错误: {task.errorCount}
                    </span>
                  </div>
                </td>
                <td>
                  <button
                    className="btn btn-primary btn-small"
                    onClick={() => handleExportTask(task)}
                    title="导出报告"
                  >
                    导出
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* 导出配置对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        task={selectedTask}
        onExport={handleExport}
      />

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default ReportExport;
