import React, { useMemo, useCallback } from 'react';

const Layout = ({ children, activeTab, setActiveTab }) => {
  'use memo';

  const tabs = useMemo(() => [
    { id: 'servers', label: '服务器管理' },
    { id: 'config', label: '核查配置' },
    { id: 'task', label: '任务管理' },
    { id: 'report', label: '报告导出' }
  ], []);

  const handleTabClick = useCallback((tabId) => {
    setActiveTab(tabId);
  }, [setActiveTab]);

  const tabButtons = useMemo(() =>
    tabs.map(tab => (
      <button
        key={tab.id}
        className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
        onClick={() => handleTabClick(tab.id)}
      >
        <span className="tab-label">{tab.label}</span>
      </button>
    )), [tabs, activeTab, handleTabClick]);

  return (
    <div className="app-container">
      {/* 导航标签栏 */}
      <header className="app-toolbar">
        <nav className="app-nav-tabs">
          {tabButtons}
        </nav>
      </header>

      {/* 主内容区域 */}
      <main className="app-main">
        <div className="content-panel">
          {children}
        </div>
      </main>

      {/* 桌面应用状态栏 */}
      <footer className="app-statusbar">
        <div className="status-left">
          <div className="status-item">
            <div className="status-indicator"></div>
            <span>连接状态: 已连接</span>
          </div>
        </div>
        <div className="status-right">
          <div className="status-item">
            <span>📦 版本: v1.0.0</span>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
