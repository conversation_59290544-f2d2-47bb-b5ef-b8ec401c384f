import React, { useEffect, useRef } from 'react';

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'medium', // small, medium, large, fullscreen
  variant = 'default', // default, success, warning, error, info
  closable = true,
  actions = null,
  className = ''
}) => {
  const modalRef = useRef(null);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && closable) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, closable, onClose]);

  // 处理点击遮罩关闭
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && closable) {
      onClose();
    }
  };

  // 聚焦管理
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="dialog-overlay"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="dialog-title"
    >
      <div
        ref={modalRef}
        className={`dialog dialog-${size} dialog-${variant} ${className}`}
        tabIndex={-1}
      >
        {title && (
          <div className="dialog-header" style={{ justifyContent: 'center' }}>
            <h2 id="dialog-title" className="dialog-title">
              {title}
            </h2>
          </div>
        )}

        <div className="dialog-content">
          {children}
        </div>

        {actions && (
          <div className="dialog-footer" style={{ justifyContent: 'center', gap: '16px' }}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

// 消息对话框组件
export const MessageModal = ({
  isOpen,
  onClose,
  title = '提示',
  message,
  variant = 'info',
  confirmText = '确定',
  size = 'small'
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      variant={variant}
      size={size}
      actions={
        <button className="btn btn-primary" onClick={onClose}>
          {confirmText}
        </button>
      }
    >
      <div className="dialog-message">
        <p>{message}</p>
      </div>
    </Modal>
  );
};

// 确认对话框组件
export const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = '确认操作',
  message,
  variant = 'warning',
  confirmText = '确定',
  cancelText = '取消',
  size = 'small',
  destructive = false
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      variant={variant}
      size={size}
      actions={
        <>
          <button className="btn btn-secondary" onClick={onClose}>
            {cancelText}
          </button>
          <button
            className={`btn ${destructive ? 'btn-danger' : 'btn-primary'}`}
            onClick={handleConfirm}
          >
            {confirmText}
          </button>
        </>
      }
    >
      <div className="dialog-message">
        <p>{message}</p>
      </div>
    </Modal>
  );
};

// 表单对话框组件
export const FormModal = ({
  isOpen,
  onClose,
  title,
  children,
  onSubmit,
  submitText = '保存',
  cancelText = '取消',
  size = 'medium',
  variant = 'default',
  loading = false
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      variant={variant}
      size={size}
      actions={
        <>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            form="dialog-form"
            disabled={loading}
          >
            {loading ? '保存中...' : submitText}
          </button>
        </>
      }
    >
      <form id="dialog-form" onSubmit={handleSubmit}>
        {children}
      </form>
    </Modal>
  );
};

export default Modal;
