import React, { useState } from 'react';
import { MessageModal } from './Modal';
import { StatusIcon } from './Icons';
import { useServerData } from '../hooks/useServerData';

const CreateTask = () => {
  const [taskInfo, setTaskInfo] = useState({
    name: '等保合规检查_2024-01-15',
    description: '定期安全合规检查',
    executeType: 'immediate'
  });

  // 使用真实的服务器数据
  const { servers } = useServerData();

  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTask, setCurrentTask] = useState('');
  const [logs, setLogs] = useState([]);
  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  // 状态图标函数已移除

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return '在线';
      case 'offline': return '离线';
      default: return '未知';
    }
  };

  const toggleServerSelection = (id) => {
    setServers(servers.map(server => 
      server.id === id ? { ...server, selected: !server.selected } : server
    ));
  };

  const selectAll = () => {
    setServers(servers.map(server => ({ ...server, selected: true })));
  };

  const invertSelection = () => {
    setServers(servers.map(server => ({ ...server, selected: !server.selected })));
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const refreshStatus = () => {
    showMessageModal('刷新状态', '正在刷新服务器状态...', 'info');
  };

  const previewTask = () => {
    const selectedServers = servers.filter(s => s.selected);
    const previewMessage = `任务名称: ${taskInfo.name}\n选中服务器: ${selectedServers.length}台\n预计用时: 约${selectedServers.length * 3}分钟`;
    showMessageModal('任务预览', previewMessage, 'info');
  };

  const startExecution = () => {
    const selectedServers = servers.filter(s => s.selected);
    if (selectedServers.length === 0) {
      showMessageModal('提示', '请至少选择一台服务器', 'warning');
      return;
    }
    
    setShowProgressDialog(true);
    setProgress(0);
    setCurrentTask('正在初始化检查任务...');
    setLogs([]);
    
    // 执行真实的检查任务
    executeRealTask(selectedServers);
  };

  const executeRealTask = (selectedServers) => {
    // 这里应该调用真实的后端API来执行检查任务
    // 目前暂时显示提示信息
    showMessageModal('功能开发中', '检查任务执行功能正在开发中，请等待后续版本', 'info');
    setShowProgressDialog(false);
  };

  const stopExecution = () => {
    setShowProgressDialog(false);
    setProgress(0);
    setCurrentTask('');
    setLogs([]);
  };

  const selectedCount = servers.filter(s => s.selected).length;
  const configStats = {
    total: 0,
    high: 0,
    medium: 0,
    low: 0,
    estimatedTime: selectedCount * 3,
    lastCompliance: 0
  };

  return (
    <div className="create-task">
      <div className="page-header">
        <h2>▶️ 创建检查任务</h2>
        <div className="header-actions">
          <button className="btn btn-secondary" onClick={previewTask}>
            预览任务
          </button>
          <button className="btn btn-primary" onClick={startExecution}>
            开始执行
          </button>
          <button className="btn btn-secondary">
            取消
          </button>
        </div>
      </div>

      <div className="task-info-bar">
        <div className="info-item">
          <label>任务名称:</label>
          <input
            type="text"
            value={taskInfo.name}
            onChange={(e) => setTaskInfo({...taskInfo, name: e.target.value})}
            className="task-name-input"
          />
        </div>
        <div className="info-item">
          <label>任务描述:</label>
          <input
            type="text"
            value={taskInfo.description}
            onChange={(e) => setTaskInfo({...taskInfo, description: e.target.value})}
            className="task-desc-input"
          />
        </div>
        <div className="info-item">
          <label>执行时间:</label>
          <div className="radio-group">
            <label>
              <input
                type="radio"
                value="immediate"
                checked={taskInfo.executeType === 'immediate'}
                onChange={(e) => setTaskInfo({...taskInfo, executeType: e.target.value})}
              />
              立即执行
            </label>
            <label>
              <input
                type="radio"
                value="scheduled"
                checked={taskInfo.executeType === 'scheduled'}
                onChange={(e) => setTaskInfo({...taskInfo, executeType: e.target.value})}
              />
              定时执行
            </label>
          </div>
        </div>
        <div className="info-item config-preview">
          <div className="preview-stats">
            <span>
              检查项: {configStats.total}项
            </span>
            <span>
              高危: {configStats.high}项
            </span>
            <span>
              中危: {configStats.medium}项
            </span>
            <span>
              低危: {configStats.low}项
            </span>
            <span>
              预计: {configStats.estimatedTime}分钟
            </span>
            <span>
              上次合规率: {configStats.lastCompliance}%
            </span>
          </div>
        </div>
      </div>

      <div className="server-table-container">
        <table className="server-table">
          <thead>
            <tr>
              <th width="50">☑️</th>
              <th width="150">服务器名称</th>
              <th width="120">IP地址</th>
              <th width="80">状态</th>
              <th width="100">最后检查</th>
            </tr>
          </thead>
          <tbody>
            {servers.map(server => (
              <tr key={server.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={server.selected}
                    onChange={() => toggleServerSelection(server.id)}
                  />
                </td>
                <td>{server.name}</td>
                <td>{server.ip}</td>
                <td>
                  <span className={`status status-${server.status}`}>
                    {getStatusText(server.status)}
                  </span>
                </td>
                <td>{server.lastCheck}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="server-actions">
        <div className="selection-info">
          已选择: {selectedCount}台服务器
        </div>
        <div className="action-buttons">
          <button className="btn btn-secondary" onClick={selectAll}>
            全选
          </button>
          <button className="btn btn-secondary" onClick={invertSelection}>
            反选
          </button>
          <button className="btn btn-secondary" onClick={refreshStatus}>
            刷新状态
          </button>
        </div>
      </div>

      {/* 任务执行进度对话框 */}
      {showProgressDialog && (
        <div className="dialog-overlay">
          <div className="dialog dialog-large">
            <div className="dialog-header">
              <h2 className="dialog-title">执行检查任务</h2>
              <button className="dialog-close" onClick={() => setShowProgressDialog(false)}>×</button>
            </div>
            <div className="dialog-content">
              <div className="progress-info">
                <div className="progress-bar-container">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{width: `${progress}%`}}
                    ></div>
                  </div>
                  <span className="progress-text">{progress}% ({Math.floor(selectedCount * progress / 100)}/{selectedCount}服务器完成)</span>
                </div>
                <div className="current-task">
                  当前: {currentTask}
                </div>
              </div>
              
              <div className="log-section">
                <div className="log-header">
                  <span>
                    执行日志
                  </span>
                  <button className="btn btn-small btn-secondary">
                    清空
                  </button>
                </div>
                <div className="log-container">
                  {logs.map((log, index) => (
                    <div key={index} className="log-entry">{log}</div>
                  ))}
                </div>
              </div>
            </div>
            <div className="dialog-footer">
              <div className="dialog-actions">
                <button className="btn btn-danger" onClick={stopExecution}>
                  停止检查
                </button>
                <button className="btn btn-primary">
                  查看结果
                </button>
                <button className="btn btn-secondary" onClick={() => setShowProgressDialog(false)}>
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default CreateTask;
