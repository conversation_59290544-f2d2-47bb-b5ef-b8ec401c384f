import React, { useState, useMemo, useCallback, memo } from 'react';
import { MessageModal } from './Modal';
import { useServerData } from '../hooks/useServerData';

// 优化的服务器行组件 - 使用memo防止不必要的重渲染
const ServerRow = memo(({
  server,
  onEdit,
  onDelete,
  onTestConnection,
  onToggleSelect,
  isTestingConnection,
  getStatusText
}) => (
  <tr>
    <td>
      <input
        type="checkbox"
        checked={server.selected || false}
        onChange={() => onToggleSelect(server.id)}
      />
    </td>
    <td>{server.name}</td>
    <td>{server.ip}</td>
    <td>{server.port}</td>
    <td>
      <span className={`status status-${server.status}`}>
        {getStatusText(server.status)}
      </span>
    </td>
    <td>
      <div className="action-buttons">
        <button
          className="btn btn-small"
          onClick={() => onEdit(server)}
        >
          编辑
        </button>
        <button
          className="btn btn-small btn-danger"
          onClick={() => onDelete(server.id)}
        >
          删除
        </button>
        <button
          className={isTestingConnection ? 'btn-info' : 'btn btn-small btn-warning'}
          onClick={() => onTestConnection(server)}
          disabled={isTestingConnection}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '32px',
            padding: '6px 12px',
            fontSize: '12px',
            gap: '4px'
          }}
        >
          {isTestingConnection ? (
            <>
              <span className="spinner"></span>
              测试中...
            </>
          ) : (
            '测试连接'
          )}
        </button>
      </div>
    </td>
  </tr>
));

ServerRow.displayName = 'ServerRow';

// 统计信息组件 - 使用memo优化
const ServerStats = memo(({
  total,
  selectedCount,
  onlineCount,
  offlineCount
}) => (
  <div className="stats-container">
    <div className="stat-item">
      <div className="stat-content">
        <div className="stat-label">总计</div>
        <div className="stat-value">{total}台</div>
      </div>
    </div>
    <div className="stat-item">
      <div className="stat-content">
        <div className="stat-label">已选择</div>
        <div className="stat-value">{selectedCount}台</div>
      </div>
    </div>
    <div className="stat-item">
      <div className="stat-content">
        <div className="stat-label">在线</div>
        <div className="stat-value stat-success">{onlineCount}台</div>
      </div>
    </div>
    <div className="stat-item">
      <div className="stat-content">
        <div className="stat-label">离线</div>
        <div className="stat-value stat-error">{offlineCount}台</div>
      </div>
    </div>
  </div>
));

ServerStats.displayName = 'ServerStats';

const ServerManagement = () => {

  // 使用自定义Hook管理服务器数据
  const {
    servers,
    loading,
    error,
    serverStats,
    addServer,
    updateServer,
    removeServer,
    toggleServerSelection,
    selectAllServers,
    testConnection,
    testBatchConnection,
    refetch
  } = useServerData();

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingServer, setEditingServer] = useState(null);
  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });
  const [testingServers, setTestingServers] = useState(new Set());
  const [isBatchTesting, setIsBatchTesting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [serverToDelete, setServerToDelete] = useState(null);
  const [newServer, setNewServer] = useState({
    name: '',
    ip: '',
    port: 22,
    username: 'root',
    authType: 'password',
    password: '',
    keyFile: '',
    keyPassphrase: ''
  });

  // 使用useCallback缓存工具函数，避免子组件重渲染

  const getStatusText = useCallback((status) => {
    switch (status) {
      case 'online': return '在线';
      case 'offline': return '离线';
      default: return '未知';
    }
  }, []);

  // 先定义 showMessageModal 函数
  const showMessageModal = useCallback((title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  }, []);

  const handleAddServer = () => {
    setShowAddDialog(true);
  };

  const handleSaveServer = useCallback(async () => {
    if (newServer.name && newServer.ip) {
      try {
        await addServer(newServer);
        // 清空表单
        setNewServer({
          name: '',
          ip: '',
          port: 22,
          username: 'root',
          authType: 'password',
          password: '',
          keyFile: '',
          keyPassphrase: ''
        });
        setShowAddDialog(false);
        showMessageModal('添加成功', '服务器已成功添加', 'success');
      } catch (error) {
        showMessageModal('添加失败', error.message || '添加服务器时发生错误', 'error');
      }
    } else {
      showMessageModal('验证失败', '请填写服务器名称和IP地址', 'warning');
    }
  }, [newServer, addServer, showMessageModal]);

  // 处理添加对话框关闭时清空表单
  const handleCloseAddDialog = useCallback(() => {
    setNewServer({
      name: '',
      ip: '',
      port: 22,
      username: 'root',
      authType: 'password',
      password: '',
      keyFile: '',
      keyPassphrase: ''
    });
    setShowAddDialog(false);
  }, []);

  // 添加服务器对话框中的测试连接
  const handleTestNewServerConnection = useCallback(async () => {
    if (!newServer.name || !newServer.ip) {
      showMessageModal('验证失败', '请先填写服务器名称和IP地址', 'warning');
      return;
    }

    if (newServer.authType === 'password' && !newServer.password) {
      showMessageModal('验证失败', '密码认证时密码不能为空', 'warning');
      return;
    }

    if (newServer.authType === 'key' && !newServer.keyFile) {
      showMessageModal('验证失败', '密钥认证时请选择密钥文件', 'warning');
      return;
    }

    setTestingServers(prev => new Set([...prev, 'new-server']));

    try {
      // 调用测试连接API
      const result = await window.go.main.App.TestServerConnection(
        newServer.ip,
        newServer.port || 22,
        newServer.username || 'root',
        newServer.authType || 'password',
        newServer.password || '',
        newServer.keyFile || '',
        newServer.keyPassphrase || ''
      );

      showMessageModal(
        '测试连接结果',
        result.message,
        result.success ? 'success' : 'error'
      );
    } catch (error) {
      showMessageModal('测试连接失败', error.message || '测试连接时发生错误', 'error');
    } finally {
      setTestingServers(prev => {
        const newSet = new Set(prev);
        newSet.delete('new-server');
        return newSet;
      });
    }
  }, [newServer, showMessageModal]);

  // 编辑服务器对话框中的测试连接
  const handleTestEditServerConnection = useCallback(async () => {
    if (!editingServer || !editingServer.name || !editingServer.ip) {
      showMessageModal('验证失败', '请先填写服务器名称和IP地址', 'warning');
      return;
    }

    if (editingServer.authType === 'password' && !editingServer.password) {
      showMessageModal('验证失败', '密码认证时密码不能为空', 'warning');
      return;
    }

    if (editingServer.authType === 'key' && !editingServer.keyFile) {
      showMessageModal('验证失败', '密钥认证时请选择密钥文件', 'warning');
      return;
    }

    setTestingServers(prev => new Set([...prev, 'edit-server']));

    try {
      // 调用测试连接API
      const result = await window.go.main.App.TestServerConnection(
        editingServer.ip,
        editingServer.port || 22,
        editingServer.username || 'root',
        editingServer.authType || 'password',
        editingServer.password || '',
        editingServer.keyFile || '',
        editingServer.keyPassphrase || ''
      );

      showMessageModal(
        '测试连接结果',
        result.message,
        result.success ? 'success' : 'error'
      );
    } catch (error) {
      showMessageModal('测试连接失败', error.message || '测试连接时发生错误', 'error');
    } finally {
      setTestingServers(prev => {
        const newSet = new Set(prev);
        newSet.delete('edit-server');
        return newSet;
      });
    }
  }, [editingServer, showMessageModal]);

  const handleDeleteServer = useCallback((id) => {
    const server = servers.find(s => s.id === id);
    if (server) {
      setServerToDelete(server);
      setShowDeleteConfirm(true);
    }
  }, [servers]);

  const confirmDeleteServer = useCallback(async () => {
    if (!serverToDelete) return;

    try {
      await removeServer(serverToDelete.id);
      showMessageModal('删除成功', '服务器已删除', 'success');
    } catch (error) {
      showMessageModal('删除失败', error.message || '删除服务器时发生错误', 'error');
    } finally {
      setShowDeleteConfirm(false);
      setServerToDelete(null);
    }
  }, [serverToDelete, removeServer, showMessageModal]);

  const cancelDeleteServer = useCallback(() => {
    setShowDeleteConfirm(false);
    setServerToDelete(null);
  }, []);

  // 选择相关的函数 - 使用Hook提供的方法
  const handleToggleServerSelection = useCallback(async (id) => {
    try {
      await toggleServerSelection(id);
    } catch (error) {
      showMessageModal('操作失败', error.message || '切换选择状态时发生错误', 'error');
    }
  }, [toggleServerSelection, showMessageModal]);

  const handleSelectAllServers = useCallback(async () => {
    try {
      const allSelected = servers.length > 0 && servers.every(s => s.selected);
      await selectAllServers(!allSelected);
    } catch (error) {
      showMessageModal('操作失败', error.message || '全选操作时发生错误', 'error');
    }
  }, [servers, selectAllServers, showMessageModal]);

  const handleTestConnection = useCallback(async (server) => {
    setTestingServers(prev => new Set([...prev, server.id]));

    try {
      // 调用后端API测试连接
      const result = await testConnection(server.id);

      showMessageModal(
        '测试连接结果',
        result.message,
        result.success ? 'success' : 'error'
      );
    } catch (error) {
      showMessageModal('测试连接失败', error.message || `测试 ${server.name} 时发生错误`, 'error');
    } finally {
      setTestingServers(prev => {
        const newSet = new Set(prev);
        newSet.delete(server.id);
        return newSet;
      });
    }
  }, [testConnection, showMessageModal]);

  const handleBatchTestConnection = useCallback(async () => {
    const selectedServers = servers.filter(s => s.selected);

    if (selectedServers.length === 0) {
      showMessageModal('提示', '请先选择要测试的服务器', 'warning');
      return;
    }

    // 设置批量测试状态
    setIsBatchTesting(true);
    // 设置所有选中服务器为测试中状态
    const selectedIds = new Set(selectedServers.map(s => s.id));
    setTestingServers(selectedIds);

    try {
      // 调用后端API批量测试连接
      const results = await testBatchConnection();

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      showMessageModal(
        '批量测试完成',
        `测试完成：${successCount} 台成功，${failCount} 台失败`,
        successCount > failCount ? 'success' : 'warning'
      );
    } catch (error) {
      showMessageModal('批量测试', `批量测试过程中发生错误: ${error.message}`, 'error');
    } finally {
      setIsBatchTesting(false);
      setTestingServers(new Set());
    }
  }, [servers, showMessageModal, testBatchConnection]);

  const handleEditServer = useCallback((server) => {
    setEditingServer({
      id: server.id,
      name: server.name,
      ip: server.ip,
      port: server.port,
      username: server.username || 'root',
      authType: server.authType || 'password',
      password: server.password || '',
      keyFile: server.keyFile || '',
      keyPassphrase: server.keyPassphrase || ''
    });
    setShowEditDialog(true);
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (editingServer && editingServer.name && editingServer.ip) {
      try {
        await updateServer(editingServer.id, editingServer);
        setShowEditDialog(false);
        setEditingServer(null);
        showMessageModal('编辑成功', '服务器信息已更新', 'success');
      } catch (error) {
        showMessageModal('编辑失败', error.message || '更新服务器信息时发生错误', 'error');
      }
    }
  }, [editingServer, updateServer, showMessageModal]);

  // 使用Hook提供的统计数据，避免重复计算
  const selectedCount = useMemo(() => servers.filter(s => s.selected).length, [servers]);
  const { total: totalServers, online: onlineCount, offline: offlineCount } = serverStats;

  return (
    <div className="server-management">
      <div className="page-header">
        <h2 className="page-title">
          服务器管理
        </h2>
        <div className="page-actions">
          <button className="btn btn-primary" onClick={handleAddServer}>
            添加服务器
          </button>
          <button
            className={`btn ${isBatchTesting ? 'btn-info' : 'btn-warning'}`}
            onClick={handleBatchTestConnection}
            disabled={selectedCount === 0 || isBatchTesting}
          >
            {isBatchTesting ? (
              <>
                <span className="spinner"></span>
                批量测试中...
              </>
            ) : (
              '测试连接'
            )}
          </button>
          <button className="btn btn-secondary" onClick={refetch}>
            刷新
          </button>
        </div>
      </div>

      {/* 服务器统计信息 - 使用优化的组件 */}
      <ServerStats
        total={totalServers}
        selectedCount={selectedCount}
        onlineCount={onlineCount}
        offlineCount={offlineCount}
      />

      <div className="table-container">
        <div className="table-content">
          <table className="table">
          <thead>
            <tr>
              <th width="50">
                <input
                  type="checkbox"
                  checked={servers.length > 0 && servers.every(s => s.selected)}
                  onChange={handleSelectAllServers}
                />
              </th>
              <th width="140">服务器名称</th>
              <th width="120">IP地址</th>
              <th width="60">端口</th>
              <th width="80">状态</th>
              <th width="160">操作</th>
            </tr>
          </thead>
          <tbody>
            {servers.map(server => (
              <ServerRow
                key={server.id}
                server={server}
                onEdit={handleEditServer}
                onDelete={handleDeleteServer}
                onTestConnection={handleTestConnection}
                onToggleSelect={handleToggleServerSelection}
                isTestingConnection={testingServers.has(server.id)}
                getStatusText={getStatusText}
              />
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* 添加服务器对话框 */}
      {showAddDialog && (
        <div className="dialog-overlay">
          <div className="dialog dialog-medium">
            <div className="dialog-header" style={{ justifyContent: 'center' }}>
              <h2 className="dialog-title">添加服务器</h2>
            </div>
            <div className="dialog-content">
              <div className="dialog-section">
                <h4>基本信息</h4>
                <div className="form-group">
                  <label>服务器名称:</label>
                  <input
                    type="text"
                    value={newServer.name}
                    onChange={(e) => setNewServer({...newServer, name: e.target.value})}
                    placeholder="Web服务器01"
                  />
                </div>
                <div className="form-group">
                  <label>IP地址:</label>
                  <input
                    type="text"
                    value={newServer.ip}
                    onChange={(e) => setNewServer({...newServer, ip: e.target.value})}
                    placeholder="*************"
                  />
                </div>
                <div className="form-group">
                  <label>SSH端口:</label>
                  <input
                    type="number"
                    value={newServer.port}
                    onChange={(e) => setNewServer({...newServer, port: parseInt(e.target.value)})}
                  />
                </div>
              </div>

              <div className="dialog-section">
                <h4>认证配置</h4>
                <div className="form-group">
                  <label>用户名:</label>
                  <input
                    type="text"
                    value={newServer.username}
                    onChange={(e) => setNewServer({...newServer, username: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>认证方式:</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        value="password"
                        checked={newServer.authType === 'password'}
                        onChange={(e) => setNewServer({...newServer, authType: e.target.value})}
                      />
                      密码认证
                    </label>
                    <label>
                      <input
                        type="radio"
                        value="key"
                        checked={newServer.authType === 'key'}
                        onChange={(e) => setNewServer({...newServer, authType: e.target.value})}
                      />
                      密钥认证
                    </label>
                  </div>
                </div>
                {newServer.authType === 'password' ? (
                  <div className="form-group">
                    <label>密码:</label>
                    <input
                      type="password"
                      value={newServer.password}
                      onChange={(e) => setNewServer({...newServer, password: e.target.value})}
                    />
                  </div>
                ) : (
                  <>
                    <div className="form-group">
                      <label>密钥文件:</label>
                      <div className="file-input">
                        <input
                          type="text"
                          value={newServer.keyFile}
                          onChange={(e) => setNewServer({...newServer, keyFile: e.target.value})}
                          placeholder="选择文件..."
                          readOnly
                        />
                        <button className="btn btn-secondary btn-small">
                          选择
                        </button>
                      </div>
                    </div>
                    <div className="form-group">
                      <label>密钥密码 (可选):</label>
                      <input
                        type="password"
                        value={newServer.keyPassphrase}
                        onChange={(e) => setNewServer({...newServer, keyPassphrase: e.target.value})}
                        placeholder="如果密钥有密码保护，请输入"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="dialog-footer" style={{ justifyContent: 'center', gap: '16px' }}>
              <button
                className={testingServers.has('new-server') ? 'btn-info' : 'btn btn-warning'}
                onClick={handleTestNewServerConnection}
                disabled={testingServers.has('new-server')}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '40px',
                  padding: '8px 16px',
                  fontSize: '14px',
                  gap: '6px',
                  minWidth: '100px'
                }}
              >
                {testingServers.has('new-server') ? (
                  <>
                    <span className="spinner"></span>
                    测试中...
                  </>
                ) : (
                  '测试连接'
                )}
              </button>
              <button
                className="btn btn-secondary"
                onClick={handleCloseAddDialog}
                style={{ minWidth: '80px' }}
              >
                取消
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveServer}
                style={{ minWidth: '80px' }}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑服务器对话框 */}
      {showEditDialog && editingServer && (
        <div className="dialog-overlay">
          <div className="dialog dialog-medium">
            <div className="dialog-header" style={{ justifyContent: 'center' }}>
              <h2 className="dialog-title">编辑服务器</h2>
            </div>
            <div className="dialog-content">
              <div className="dialog-section">
                <h4>基本信息</h4>
                <div className="form-group">
                  <label>服务器名称:</label>
                  <input
                    type="text"
                    value={editingServer.name}
                    onChange={(e) => setEditingServer({...editingServer, name: e.target.value})}
                    placeholder="Web服务器01"
                  />
                </div>
                <div className="form-group">
                  <label>IP地址:</label>
                  <input
                    type="text"
                    value={editingServer.ip}
                    onChange={(e) => setEditingServer({...editingServer, ip: e.target.value})}
                    placeholder="*************"
                  />
                </div>
                <div className="form-group">
                  <label>SSH端口:</label>
                  <input
                    type="number"
                    value={editingServer.port}
                    onChange={(e) => setEditingServer({...editingServer, port: parseInt(e.target.value)})}
                  />
                </div>
              </div>

              <div className="dialog-section">
                <h4>认证配置</h4>
                <div className="form-group">
                  <label>用户名:</label>
                  <input
                    type="text"
                    value={editingServer.username}
                    onChange={(e) => setEditingServer({...editingServer, username: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>认证方式:</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        value="password"
                        checked={editingServer.authType === 'password'}
                        onChange={(e) => setEditingServer({...editingServer, authType: e.target.value})}
                      />
                      密码认证
                    </label>
                    <label>
                      <input
                        type="radio"
                        value="key"
                        checked={editingServer.authType === 'key'}
                        onChange={(e) => setEditingServer({...editingServer, authType: e.target.value})}
                      />
                      密钥认证
                    </label>
                  </div>
                </div>
                {editingServer.authType === 'password' ? (
                  <div className="form-group">
                    <label>密码:</label>
                    <input
                      type="password"
                      value={editingServer.password}
                      onChange={(e) => setEditingServer({...editingServer, password: e.target.value})}
                    />
                  </div>
                ) : (
                  <>
                    <div className="form-group">
                      <label>密钥文件:</label>
                      <div className="file-input">
                        <input
                          type="text"
                          value={editingServer.keyFile}
                          onChange={(e) => setEditingServer({...editingServer, keyFile: e.target.value})}
                          placeholder="选择文件..."
                          readOnly
                        />
                        <button className="btn-file">
                          选择
                        </button>
                      </div>
                    </div>
                    <div className="form-group">
                      <label>密钥密码 (可选):</label>
                      <input
                        type="password"
                        value={editingServer.keyPassphrase}
                        onChange={(e) => setEditingServer({...editingServer, keyPassphrase: e.target.value})}
                        placeholder="如果密钥有密码保护，请输入"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="dialog-footer" style={{ justifyContent: 'center', gap: '16px' }}>
              <button
                className={testingServers.has('edit-server') ? 'btn-info' : 'btn btn-warning'}
                onClick={handleTestEditServerConnection}
                disabled={testingServers.has('edit-server')}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '40px',
                  padding: '8px 16px',
                  fontSize: '14px',
                  gap: '6px',
                  minWidth: '100px'
                }}
              >
                {testingServers.has('edit-server') ? (
                  <>
                    <span className="spinner"></span>
                    测试中...
                  </>
                ) : (
                  '测试连接'
                )}
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowEditDialog(false)}
                style={{ minWidth: '80px' }}
              >
                取消
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveEdit}
                style={{ minWidth: '80px' }}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && serverToDelete && (
        <div className="dialog-overlay">
          <div className="dialog dialog-small">
            <div className="dialog-header" style={{ justifyContent: 'center' }}>
              <h2 className="dialog-title">确认删除</h2>
            </div>
            <div className="dialog-content" style={{ textAlign: 'center' }}>
              <p>确定要删除服务器 <strong>{serverToDelete.name}</strong> ({serverToDelete.ip}) 吗？</p>
              <p className="text-warning">此操作不可撤销！</p>
            </div>
            <div className="dialog-footer" style={{ justifyContent: 'center', gap: '16px' }}>
              <button
                className="btn btn-secondary"
                onClick={cancelDeleteServer}
                style={{ minWidth: '80px' }}
              >
                取消
              </button>
              <button
                className="btn btn-danger"
                onClick={confirmDeleteServer}
                style={{ minWidth: '80px' }}
              >
                确定删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

// 使用memo包装主组件，提升性能
export default memo(ServerManagement);
