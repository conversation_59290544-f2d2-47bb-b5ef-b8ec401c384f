import { useState, useMemo, useCallback, lazy, Suspense } from 'react';
import './styles/unified-design-system.css';
import './App.css';
import Layout from './components/Layout';
import ErrorBoundary from './components/ErrorBoundary';
import { Spinner } from './components/LoadingStates';

// 懒加载组件以提升初始加载性能
const ServerManagement = lazy(() => import('./components/ServerManagement'));
const CheckConfig = lazy(() => import('./components/CheckConfig'));
const TaskList = lazy(() => import('./components/TaskList'));
const ReportExport = lazy(() => import('./components/ReportExport'));

// 优化的加载组件
const LoadingSpinner = () => (
  <Spinner size="large" text="正在加载组件..." />
);

function App() {
  const [activeTab, setActiveTab] = useState('servers');

  // 使用useCallback缓存函数，避免子组件不必要的重渲染
  const handleTabChange = useCallback((tabId) => {
    setActiveTab(tabId);
  }, []);

  // 使用useMemo缓存组件渲染，只在activeTab变化时重新计算
  const renderContent = useMemo(() => {
    const componentMap = {
      servers: ServerManagement,
      config: CheckConfig,
      task: TaskList,
      report: ReportExport,
    };

    const Component = componentMap[activeTab] || ServerManagement;

    return (
      <Suspense fallback={<LoadingSpinner />}>
        <Component />
      </Suspense>
    );
  }, [activeTab]);

  return (
    <ErrorBoundary>
      <Layout activeTab={activeTab} setActiveTab={handleTabChange}>

        {renderContent}
      </Layout>
    </ErrorBoundary>
  );
}

export default App;
