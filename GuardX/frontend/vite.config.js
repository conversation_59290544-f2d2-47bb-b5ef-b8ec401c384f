import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],

  // 性能优化配置
  optimizeDeps: {
    // 预构建依赖，提升冷启动速度
    include: ['react', 'react-dom'],
    // 强制优化，忽略缓存
    force: false,
    // 优化策略
    holdUntilCrawlEnd: false // 提升大型项目的服务器冷启动性能
  },

  // 服务器配置
  server: {
    // 预热文件，提升初始页面加载性能
    warmup: {
      clientFiles: [
        './src/App.jsx',
        './src/components/*.jsx',
        './src/main.jsx'
      ]
    }
  },

  // 构建优化
  build: {
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 生成 source map
    sourcemap: false,
    // 压缩配置
    minify: 'esbuild',
    // 分包策略
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 React 相关库分离到单独的 chunk
          react: ['react', 'react-dom'],
          // 将组件库分离
          components: [
            './src/components/Layout',
            './src/components/ServerManagement',
            './src/components/CheckConfig',
            './src/components/TaskList',
            './src/components/ReportExport'
          ]
        }
      }
    }
  },

  // CSS 优化
  css: {
    // 启用 CSS 预处理器多线程
    preprocessorMaxWorkers: true
  }
})
