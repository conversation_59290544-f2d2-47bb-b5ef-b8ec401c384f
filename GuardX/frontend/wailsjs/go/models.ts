export namespace models {
	
	export class Server {
	    id: number;
	    name: string;
	    ip: string;
	    port: number;
	    username: string;
	    authType: string;
	    password?: string;
	    keyFile?: string;
	    keyPassphrase?: string;
	    status: string;
	    selected: boolean;
	    // Go type: time
	    lastCheck?: any;
	    // Go type: time
	    createdAt: any;
	    // Go type: time
	    updatedAt: any;
	
	    static createFrom(source: any = {}) {
	        return new Server(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.ip = source["ip"];
	        this.port = source["port"];
	        this.username = source["username"];
	        this.authType = source["authType"];
	        this.password = source["password"];
	        this.keyFile = source["keyFile"];
	        this.keyPassphrase = source["keyPassphrase"];
	        this.status = source["status"];
	        this.selected = source["selected"];
	        this.lastCheck = this.convertValues(source["lastCheck"], null);
	        this.createdAt = this.convertValues(source["createdAt"], null);
	        this.updatedAt = this.convertValues(source["updatedAt"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ServerStats {
	    total: number;
	    selected: number;
	    online: number;
	    offline: number;
	
	    static createFrom(source: any = {}) {
	        return new ServerStats(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.total = source["total"];
	        this.selected = source["selected"];
	        this.online = source["online"];
	        this.offline = source["offline"];
	    }
	}
	export class TestConnectionResult {
	    serverId: number;
	    success: boolean;
	    message: string;
	    status: string;
	
	    static createFrom(source: any = {}) {
	        return new TestConnectionResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.serverId = source["serverId"];
	        this.success = source["success"];
	        this.message = source["message"];
	        this.status = source["status"];
	    }
	}

}

