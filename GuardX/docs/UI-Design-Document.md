# GuardX UI 设计文档

## 📋 项目概述

**项目名称**: GuardX - 服务器安全检查工具  
**设计风格**: 现代极简主义  
**设计理念**: 纯文字界面，无图标设计  
**目标用户**: 系统管理员、安全工程师  

## 🎨 设计系统

### 色彩方案

#### 主色调
- **主要色**: `#3b82f6` (蓝色) - 主要按钮、链接
- **次要色**: `#64748b` (灰蓝色) - 次要文字
- **背景色**: `#f8fafc` (浅灰色) - 页面背景

#### 状态色彩
- **成功色**: `#22c55e` (绿色) - 成功状态、在线状态
- **警告色**: `#f59e0b` (橙色) - 警告状态
- **错误色**: `#ef4444` (红色) - 错误状态、离线状态
- **信息色**: `#3b82f6` (蓝色) - 信息提示

#### 文字颜色
- **主要文字**: `#1e293b` (深灰色)
- **次要文字**: `#475569` (中灰色)
- **辅助文字**: `#64748b` (浅灰色)

### 字体系统

#### 字体族
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

#### 字体尺寸
- **大标题**: 24px (页面标题)
- **中标题**: 18px (对话框标题)
- **正文**: 14px (按钮、表格内容)
- **小字**: 12px (状态标签、辅助信息)

#### 字重
- **粗体**: 600 (标题、重要按钮)
- **中等**: 500 (普通按钮、标签)
- **常规**: 400 (正文内容)

### 间距系统

#### 内边距
- **小**: 4px (状态标签)
- **中**: 8px (按钮内边距)
- **大**: 16px (卡片内边距)
- **特大**: 24px (对话框内边距)

#### 外边距
- **组件间距**: 12px
- **区块间距**: 20px
- **页面边距**: 24px

### 圆角系统
- **小圆角**: 6px (按钮、输入框)
- **中圆角**: 8px (卡片、对话框)
- **大圆角**: 12px (状态标签)

## 🏗️ 布局结构

### 整体布局
```
┌─────────────────────────────────────────┐
│              原生标题栏                  │
├─────────────────────────────────────────┤
│  服务器管理 | 核查配置 | 任务管理 | 报告导出 │
├─────────────────────────────────────────┤
│                                         │
│              页面内容区域                │
│                                         │
└─────────────────────────────────────────┘
```

### 导航系统
- **标签页导航**: 水平排列的文字标签
- **无图标设计**: 纯文字导航，简洁明了
- **激活状态**: 蓝色背景，白色文字

### 页面结构
```
┌─────────────────────────────────────────┐
│  页面标题                                │
├─────────────────────────────────────────┤
│  统计信息卡片区域 (4个卡片)              │
│  [总数] [已选择] [在线] [离线]           │
├─────────────────────────────────────────┤
│  操作按钮区域                            │
│  [添加服务器] [测试连接] [刷新]          │
├─────────────────────────────────────────┤
│  数据表格/内容区域 (带复选框)            │
│  [☑] [名称] [IP] [端口] [状态] [操作]    │
└─────────────────────────────────────────┘
```

## 🧩 组件设计

### 按钮系统

#### 基础按钮
- **尺寸**: 40px 高度，10px 16px 内边距
- **圆角**: 8px
- **字体**: 14px，500字重
- **过渡**: 0.2s 缓动动画

#### 按钮变体
1. **主要按钮** (`btn-primary`)
   - 背景: 蓝色渐变 `#3b82f6 → #2563eb`
   - 文字: 白色
   - 用途: 主要操作

2. **次要按钮** (`btn-secondary`)
   - 背景: `#f8fafc`
   - 文字: `#64748b`
   - 用途: 次要操作

3. **成功按钮** (`btn-success`)
   - 背景: 绿色渐变 `#10b981 → #059669`
   - 文字: 白色
   - 用途: 确认操作

4. **警告按钮** (`btn-warning`)
   - 背景: 橙色渐变 `#f59e0b → #d97706`
   - 文字: 白色
   - 用途: 警告操作

5. **危险按钮** (`btn-danger`)
   - 背景: 红色渐变 `#ef4444 → #dc2626`
   - 文字: 白色
   - 用途: 删除操作

#### 按钮尺寸
- **小按钮**: 32px 高度，6px 12px 内边距
- **标准按钮**: 40px 高度，10px 16px 内边距
- **大按钮**: 48px 高度，14px 24px 内边距

#### 交互效果
- **悬停**: 上浮1px + 增强阴影 + 光泽扫过
- **点击**: 回到原位 + 阴影减弱
- **禁用**: 50%透明度 + 禁用鼠标事件

### 状态标签

#### 设计规范
- **布局**: `inline-block`
- **内边距**: 4px 12px
- **圆角**: 12px
- **字体**: 12px，500字重
- **对齐**: 居中对齐

#### 状态变体
1. **在线状态**
   - 背景: `#dcfce7`
   - 文字: `#166534`
   - 边框: `#bbf7d0`

2. **离线状态**
   - 背景: `#fef2f2`
   - 文字: `#991b1b`
   - 边框: `#fecaca`

### 表格系统

#### 表格结构
- **表头**: 灰色背景，粗体文字
- **表格行**: 白色背景，悬停时浅灰色
- **边框**: 1px 浅灰色分隔线
- **内边距**: 12px 16px

#### 列结构 (6列)
1. **复选框列**: 50px 宽度，居中对齐
2. **服务器名称**: 140px 宽度，左对齐
3. **IP地址**: 120px 宽度，左对齐
4. **端口**: 60px 宽度，左对齐
5. **状态**: 80px 宽度，居中对齐
6. **操作**: 160px 宽度，居中对齐

#### 选择功能
- **表头复选框**: 全选/取消全选所有服务器
- **行复选框**: 单个服务器选择/取消选择
- **选择状态**: 实时更新统计信息中的"已选择"数量

### 对话框系统

#### 设计规范
- **遮罩**: 50%透明黑色 + 12px模糊
- **主体**: 白色背景，12px圆角
- **阴影**: 多层阴影系统
- **动画**: 0.3s缓动动画

#### 尺寸变体
- **小对话框**: 400px 宽度
- **中对话框**: 500px 宽度  
- **大对话框**: 700px 宽度
- **全屏对话框**: 95vw × 95vh

#### 结构组成
1. **头部** (`dialog-header`)
   - 标题 + 关闭按钮
   - 背景: `rgba(248, 250, 252, 0.5)`
   - 内边距: 20px 24px

2. **内容** (`dialog-content`)
   - 滚动内容区域
   - 内边距: 24px

3. **底部** (`dialog-footer`)
   - 操作按钮区域
   - 背景: `rgba(248, 250, 252, 0.3)`
   - 内边距: 16px 24px

#### 对话框类型
1. **消息对话框**: 显示提示信息
2. **确认对话框**: 确认危险操作
3. **表单对话框**: 表单输入和提交

## 📱 响应式设计

### 断点系统
- **桌面**: ≥1024px
- **平板**: 768px - 1023px
- **手机**: <768px

### 移动端适配
- **对话框**: 95vw宽度，垂直按钮布局
- **表格**: 水平滚动
- **按钮**: 全宽显示，44px最小高度

## ♿ 无障碍设计

### 颜色对比
- **文字对比度**: 符合WCAG AA标准
- **状态区分**: 不仅依赖颜色，还有文字说明

### 键盘导航
- **Tab导航**: 所有交互元素可Tab访问
- **ESC关闭**: 对话框支持ESC键关闭
- **回车确认**: 表单支持回车提交

### 屏幕阅读器
- **ARIA标签**: 完整的aria-label和role属性
- **语义化**: 使用语义化HTML标签
- **焦点管理**: 合理的焦点顺序

## 🎯 设计原则

### 极简主义
- **无图标设计**: 所有界面元素使用纯文字
- **简洁布局**: 减少视觉干扰，突出核心功能
- **统一风格**: 整个应用保持一致的视觉语言

### 功能优先
- **信息直观**: 文字比图标更直接明确
- **操作清晰**: 按钮功能一目了然
- **状态明确**: 状态信息清晰易懂

### 现代化
- **渐变背景**: 微妙的渐变增加层次感
- **流畅动画**: 0.2-0.3s的流畅过渡动画
- **精致阴影**: 多层阴影营造深度感

## 🔄 交互设计

### 按钮交互
1. **默认状态**: 基础样式
2. **悬停状态**: 上浮 + 阴影增强 + 光泽扫过
3. **点击状态**: 下压 + 阴影减弱
4. **禁用状态**: 透明度降低 + 禁用交互

### 对话框交互
1. **打开动画**: 缩放 + 淡入 + 模糊到清晰
2. **关闭方式**: 点击遮罩 / ESC键 / 关闭按钮
3. **焦点管理**: 自动聚焦到对话框

### 表格交互
1. **行悬停**: 背景色变化
2. **状态标签**: 静态显示，无交互
3. **操作按钮**: 标准按钮交互

## 📊 组件清单

### 基础组件
- [x] 按钮系统 (5种变体 + 3种尺寸)
- [x] 状态标签 (3种状态)
- [x] 表格组件 (带复选框选择功能)
- [x] 对话框系统 (4种类型 + 4种尺寸)
- [x] 导航标签
- [x] 统计卡片 (4个统计项)
- [x] 复选框组件 (表格选择功能)

### 页面组件
- [x] 服务器管理页面 (带单选和批量操作)
- [x] 核查配置页面
- [x] 任务管理页面
- [x] 报告导出页面

### 功能组件
- [x] 添加/编辑服务器对话框
- [x] 任务创建对话框
- [x] 导出设置对话框
- [x] 消息提示对话框
- [x] 确认操作对话框
- [x] 表单对话框 (新增)

### 交互功能
- [x] 服务器单选功能
- [x] 服务器全选功能 (表头复选框)
- [x] 批量测试连接
- [x] 实时统计更新

## 🎨 视觉层次

### 信息层次
1. **页面标题**: 24px，粗体，深色
2. **区块标题**: 18px，粗体，深色
3. **正文内容**: 14px，常规，中色
4. **辅助信息**: 12px，常规，浅色

### 颜色层次
1. **主要信息**: 深色文字，高对比度
2. **次要信息**: 中色文字，中对比度
3. **辅助信息**: 浅色文字，低对比度
4. **状态信息**: 彩色背景，高对比度文字

### 空间层次
1. **页面级**: 24px边距
2. **区块级**: 20px间距
3. **组件级**: 12px间距
4. **元素级**: 4-8px间距

## 🛠️ 开发指南

### CSS类名规范

#### 按钮类名
```css
.btn                 /* 基础按钮 */
.btn-primary         /* 主要按钮 */
.btn-secondary       /* 次要按钮 */
.btn-success         /* 成功按钮 */
.btn-warning         /* 警告按钮 */
.btn-danger          /* 危险按钮 */
.btn-small           /* 小尺寸按钮 */
.btn-large           /* 大尺寸按钮 */
```

#### 状态类名
```css
.status              /* 基础状态标签 */
.status-online       /* 在线状态 */
.status-offline      /* 离线状态 */
```

#### 对话框类名
```css
.dialog-overlay      /* 对话框遮罩 */
.dialog              /* 对话框主体 */
.dialog-small        /* 小对话框 */
.dialog-medium       /* 中对话框 */
.dialog-large        /* 大对话框 */
.dialog-header       /* 对话框头部 */
.dialog-title        /* 对话框标题 */
.dialog-close        /* 关闭按钮 */
.dialog-content      /* 对话框内容 */
.dialog-footer       /* 对话框底部 */
.dialog-actions      /* 按钮区域 */
```

### 使用示例

#### 按钮使用
```jsx
{/* 主要操作按钮 */}
<button className="btn btn-primary">
  添加服务器
</button>

{/* 危险操作按钮 */}
<button className="btn btn-danger btn-small">
  删除
</button>

{/* 次要操作按钮 */}
<button className="btn btn-secondary">
  取消
</button>
```

#### 状态标签使用
```jsx
<span className={`status status-${server.status}`}>
  {getStatusText(server.status)}
</span>
```

#### 表格复选框使用
```jsx
{/* 表头复选框 - 全选功能 */}
<th>
  <input
    type="checkbox"
    checked={servers.length > 0 && servers.every(s => s.selected)}
    onChange={selectAllServers}
  />
</th>

{/* 行复选框 - 单选功能 */}
<td>
  <input
    type="checkbox"
    checked={server.selected || false}
    onChange={() => onToggleSelect(server.id)}
  />
</td>
```

#### 对话框使用
```jsx
<div className="dialog-overlay">
  <div className="dialog dialog-medium">
    <div className="dialog-header">
      <h2 className="dialog-title">添加服务器</h2>
      <button className="dialog-close">×</button>
    </div>
    <div className="dialog-content">
      {/* 对话框内容 */}
    </div>
    <div className="dialog-footer">
      <div className="dialog-actions">
        <button className="btn btn-secondary">取消</button>
        <button className="btn btn-primary">确定</button>
      </div>
    </div>
  </div>
</div>
```

## 📐 设计规格

### 按钮规格表

| 类型 | 高度 | 内边距 | 字体大小 | 圆角 | 用途 |
|------|------|--------|----------|------|------|
| 小按钮 | 32px | 6px 12px | 12px | 8px | 表格操作 |
| 标准按钮 | 40px | 10px 16px | 14px | 8px | 常规操作 |
| 大按钮 | 48px | 14px 24px | 16px | 8px | 重要操作 |

### 状态标签规格表

| 状态 | 背景色 | 文字色 | 边框色 | 含义 |
|------|--------|--------|--------|------|
| 在线 | #dcfce7 | #166534 | #bbf7d0 | 服务器正常运行 |
| 离线 | #fef2f2 | #991b1b | #fecaca | 服务器无法连接 |

### 对话框规格表

| 尺寸 | 宽度 | 最大宽度 | 用途 |
|------|------|----------|------|
| 小 | 400px | 90vw | 消息提示 |
| 中 | 500px | 90vw | 表单输入 |
| 大 | 700px | 90vw | 复杂内容 |
| 全屏 | 95vw | 95vw | 大量数据 |

## 🎨 设计资源

### 颜色调色板

#### 主色调
```css
--primary-50:  #eff6ff;
--primary-100: #dbeafe;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;
```

#### 灰色调
```css
--gray-50:  #f8fafc;
--gray-100: #f1f5f9;
--gray-200: #e2e8f0;
--gray-300: #cbd5e1;
--gray-400: #94a3b8;
--gray-500: #64748b;
--gray-600: #475569;
--gray-700: #334155;
--gray-800: #1e293b;
--gray-900: #0f172a;
```

#### 状态色调
```css
--success-50:  #f0fdf4;
--success-500: #22c55e;
--success-600: #16a34a;

--warning-50:  #fffbeb;
--warning-500: #f59e0b;
--warning-600: #d97706;

--error-50:  #fef2f2;
--error-500: #ef4444;
--error-600: #dc2626;
```

### 阴影系统
```css
--shadow-sm:   0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
--shadow-md:   0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg:   0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl:   0 20px 25px -5px rgba(0, 0, 0, 0.1);
```

### 动画系统
```css
--transition-fast: 0.15s ease-out;
--transition-base: 0.2s ease-out;
--transition-slow: 0.3s ease-out;

--easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
--easing-ease-in:  cubic-bezier(0.4, 0, 1, 1);
--easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
```

## 🔍 质量检查清单

### 视觉一致性
- [ ] 所有按钮使用统一的样式系统
- [ ] 状态标签颜色和尺寸一致
- [ ] 对话框结构和动画统一
- [ ] 字体大小和颜色符合层次规范

### 交互体验
- [ ] 所有按钮有悬停和点击反馈
- [ ] 对话框支持ESC键和遮罩点击关闭
- [ ] 表格行有悬停效果
- [ ] 加载状态有适当的视觉反馈

### 无障碍性
- [ ] 颜色对比度符合WCAG标准
- [ ] 所有交互元素可键盘访问
- [ ] 表单有适当的标签和验证
- [ ] 屏幕阅读器可正确识别内容

### 响应式设计
- [ ] 在不同屏幕尺寸下布局正常
- [ ] 移动端按钮大小符合触摸标准
- [ ] 对话框在小屏幕上适当调整
- [ ] 表格在小屏幕上可水平滚动

## 📚 参考资源

### 设计系统参考
- [Tailwind CSS](https://tailwindcss.com/) - 颜色和间距系统
- [Material Design](https://material.io/) - 交互动画参考
- [Human Interface Guidelines](https://developer.apple.com/design/) - 无障碍设计

### 技术文档
- [WCAG 2.1](https://www.w3.org/WAI/WCAG21/) - 无障碍标准
- [MDN Web Docs](https://developer.mozilla.org/) - CSS和HTML参考
- [React文档](https://react.dev/) - 组件开发指南

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**维护者**: GuardX开发团队
**审核状态**: ✅ 已审核
