{"$schema": "https://wails.io/schemas/config.v2.json", "name": "GuardX", "outputfilename": "GuardX", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "author": {"name": "q0ne", "email": "<EMAIL>"}, "info": {"productName": "GuardX", "productVersion": "1.0.0", "copyright": "Copyright © 2024", "comments": "GuardX - Linux Security Compliance Tool"}}