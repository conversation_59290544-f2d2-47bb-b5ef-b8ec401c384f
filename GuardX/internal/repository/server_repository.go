package repository

import (
	"database/sql"
	"fmt"
	"time"

	"GuardX/internal/database"
	"GuardX/internal/models"
)

// ServerRepository 服务器数据访问层
type ServerRepository struct {
	db *sql.DB
}

// NewServerRepository 创建服务器仓库实例
func NewServerRepository() *ServerRepository {
	return &ServerRepository{
		db: database.GetDB(),
	}
}

// GetAll 获取所有服务器
func (r *ServerRepository) GetAll() ([]models.Server, error) {
	query := `
		SELECT id, name, ip, port, username, auth_type, password, key_file, key_passphrase,
		       status, selected, last_check, created_at, updated_at
		FROM servers
		ORDER BY created_at DESC
	`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询服务器列表失败: %v", err)
	}
	defer rows.Close()
	
	var servers []models.Server
	for rows.Next() {
		var server models.Server
		var lastCheck sql.NullTime
		
		err := rows.Scan(
			&server.ID, &server.Name, &server.IP, &server.Port, &server.Username,
			&server.AuthType, &server.Password, &server.KeyFile, &server.KeyPassphrase,
			&server.Status, &server.Selected, &lastCheck, &server.CreatedAt, &server.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描服务器数据失败: %v", err)
		}
		
		if lastCheck.Valid {
			server.LastCheck = &lastCheck.Time
		}
		
		servers = append(servers, server)
	}
	
	return servers, nil
}

// GetByID 根据ID获取服务器
func (r *ServerRepository) GetByID(id int) (*models.Server, error) {
	query := `
		SELECT id, name, ip, port, username, auth_type, password, key_file, key_passphrase,
		       status, selected, last_check, created_at, updated_at
		FROM servers
		WHERE id = ?
	`
	
	var server models.Server
	var lastCheck sql.NullTime
	
	err := r.db.QueryRow(query, id).Scan(
		&server.ID, &server.Name, &server.IP, &server.Port, &server.Username,
		&server.AuthType, &server.Password, &server.KeyFile, &server.KeyPassphrase,
		&server.Status, &server.Selected, &lastCheck, &server.CreatedAt, &server.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("服务器不存在")
		}
		return nil, fmt.Errorf("查询服务器失败: %v", err)
	}
	
	if lastCheck.Valid {
		server.LastCheck = &lastCheck.Time
	}
	
	return &server, nil
}

// Create 创建服务器
func (r *ServerRepository) Create(req *models.CreateServerRequest) (*models.Server, error) {
	// 检查IP和端口是否已存在
	exists, err := r.ExistsByIPAndPort(req.IP, req.Port)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, fmt.Errorf("服务器 %s:%d 已存在", req.IP, req.Port)
	}
	
	query := `
		INSERT INTO servers (name, ip, port, username, auth_type, password, key_file, key_passphrase, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	now := time.Now()
	result, err := r.db.Exec(query, req.Name, req.IP, req.Port, req.Username, req.AuthType,
		req.Password, req.KeyFile, req.KeyPassphrase, now, now)
	if err != nil {
		return nil, fmt.Errorf("创建服务器失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取新服务器ID失败: %v", err)
	}
	
	return r.GetByID(int(id))
}

// Update 更新服务器
func (r *ServerRepository) Update(id int, req *models.UpdateServerRequest) error {
	// 检查服务器是否存在
	_, err := r.GetByID(id)
	if err != nil {
		return err
	}
	
	// 检查IP和端口是否与其他服务器冲突
	exists, err := r.ExistsByIPAndPortExcludeID(req.IP, req.Port, id)
	if err != nil {
		return err
	}
	if exists {
		return fmt.Errorf("服务器 %s:%d 已存在", req.IP, req.Port)
	}
	
	query := `
		UPDATE servers 
		SET name = ?, ip = ?, port = ?, username = ?, auth_type = ?, 
		    password = ?, key_file = ?, key_passphrase = ?, updated_at = ?
		WHERE id = ?
	`
	
	_, err = r.db.Exec(query, req.Name, req.IP, req.Port, req.Username, req.AuthType,
		req.Password, req.KeyFile, req.KeyPassphrase, time.Now(), id)
	if err != nil {
		return fmt.Errorf("更新服务器失败: %v", err)
	}
	
	return nil
}

// Delete 删除服务器
func (r *ServerRepository) Delete(id int) error {
	// 检查服务器是否存在
	_, err := r.GetByID(id)
	if err != nil {
		return err
	}
	
	query := "DELETE FROM servers WHERE id = ?"
	_, err = r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除服务器失败: %v", err)
	}
	
	return nil
}

// UpdateStatus 更新服务器状态
func (r *ServerRepository) UpdateStatus(id int, status string) error {
	query := "UPDATE servers SET status = ?, last_check = ?, updated_at = ? WHERE id = ?"
	now := time.Now()
	_, err := r.db.Exec(query, status, now, now, id)
	if err != nil {
		return fmt.Errorf("更新服务器状态失败: %v", err)
	}
	return nil
}

// ToggleSelection 切换服务器选择状态
func (r *ServerRepository) ToggleSelection(id int) error {
	query := "UPDATE servers SET selected = NOT selected, updated_at = ? WHERE id = ?"
	_, err := r.db.Exec(query, time.Now(), id)
	if err != nil {
		return fmt.Errorf("切换服务器选择状态失败: %v", err)
	}
	return nil
}

// SelectAll 全选/取消全选服务器
func (r *ServerRepository) SelectAll(selected bool) error {
	query := "UPDATE servers SET selected = ?, updated_at = ?"
	_, err := r.db.Exec(query, selected, time.Now())
	if err != nil {
		return fmt.Errorf("批量选择服务器失败: %v", err)
	}
	return nil
}

// GetStats 获取服务器统计信息
func (r *ServerRepository) GetStats() (*models.ServerStats, error) {
	query := `
		SELECT 
			COUNT(*) as total,
			SUM(CASE WHEN selected = 1 THEN 1 ELSE 0 END) as selected,
			SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online,
			SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline
		FROM servers
	`
	
	var stats models.ServerStats
	err := r.db.QueryRow(query).Scan(&stats.Total, &stats.Selected, &stats.Online, &stats.Offline)
	if err != nil {
		return nil, fmt.Errorf("获取服务器统计信息失败: %v", err)
	}
	
	return &stats, nil
}

// GetSelectedServers 获取已选择的服务器
func (r *ServerRepository) GetSelectedServers() ([]models.Server, error) {
	query := `
		SELECT id, name, ip, port, username, auth_type, password, key_file, key_passphrase,
		       status, selected, last_check, created_at, updated_at
		FROM servers
		WHERE selected = 1
		ORDER BY created_at DESC
	`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询已选择服务器失败: %v", err)
	}
	defer rows.Close()
	
	var servers []models.Server
	for rows.Next() {
		var server models.Server
		var lastCheck sql.NullTime
		
		err := rows.Scan(
			&server.ID, &server.Name, &server.IP, &server.Port, &server.Username,
			&server.AuthType, &server.Password, &server.KeyFile, &server.KeyPassphrase,
			&server.Status, &server.Selected, &lastCheck, &server.CreatedAt, &server.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描服务器数据失败: %v", err)
		}
		
		if lastCheck.Valid {
			server.LastCheck = &lastCheck.Time
		}
		
		servers = append(servers, server)
	}
	
	return servers, nil
}

// ExistsByIPAndPort 检查IP和端口是否已存在
func (r *ServerRepository) ExistsByIPAndPort(ip string, port int) (bool, error) {
	query := "SELECT COUNT(*) FROM servers WHERE ip = ? AND port = ?"
	var count int
	err := r.db.QueryRow(query, ip, port).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查服务器是否存在失败: %v", err)
	}
	return count > 0, nil
}

// ExistsByIPAndPortExcludeID 检查IP和端口是否已存在（排除指定ID）
func (r *ServerRepository) ExistsByIPAndPortExcludeID(ip string, port int, excludeID int) (bool, error) {
	query := "SELECT COUNT(*) FROM servers WHERE ip = ? AND port = ? AND id != ?"
	var count int
	err := r.db.QueryRow(query, ip, port, excludeID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查服务器是否存在失败: %v", err)
	}
	return count > 0, nil
}

// ExistsByName 检查指定名称的服务器是否存在
func (r *ServerRepository) ExistsByName(name string) (bool, error) {
	query := "SELECT COUNT(*) FROM servers WHERE name = ?"
	var count int
	err := r.db.QueryRow(query, name).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查服务器名称失败: %v", err)
	}
	return count > 0, nil
}

// ExistsByNameExcludeID 检查指定名称的服务器是否存在（排除指定ID）
func (r *ServerRepository) ExistsByNameExcludeID(name string, excludeID int) (bool, error) {
	query := "SELECT COUNT(*) FROM servers WHERE name = ? AND id != ?"
	var count int
	err := r.db.QueryRow(query, name, excludeID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查服务器名称失败: %v", err)
	}
	return count > 0, nil
}




