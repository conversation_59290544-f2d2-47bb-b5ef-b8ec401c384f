package service

import (
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"

	"GuardX/internal/models"
	"GuardX/internal/repository"
	"golang.org/x/crypto/ssh"
)

// ServerService 服务器服务层
type ServerService struct {
	repo *repository.ServerRepository
}

// NewServerService 创建服务器服务实例
func NewServerService() *ServerService {
	return &ServerService{
		repo: repository.NewServerRepository(),
	}
}

// GetAllServers 获取所有服务器
func (s *ServerService) GetAllServers() ([]models.Server, error) {
	return s.repo.GetAll()
}

// GetServerByID 根据ID获取服务器
func (s *ServerService) GetServerByID(id int) (*models.Server, error) {
	return s.repo.GetByID(id)
}

// CreateServer 创建服务器
func (s *ServerService) CreateServer(req *models.CreateServerRequest) (*models.Server, error) {
	// 验证服务器名称是否重复
	exists, err := s.repo.ExistsByName(req.Name)
	if err != nil {
		return nil, fmt.Errorf("检查服务器名称时发生错误: %v", err)
	}
	if exists {
		return nil, fmt.Errorf("服务器名称 \"%s\" 已存在，请使用其他名称", req.Name)
	}

	// 验证IP地址和端口是否重复
	ipExists, err := s.repo.ExistsByIPAndPort(req.IP, req.Port)
	if err != nil {
		return nil, fmt.Errorf("检查服务器IP和端口时发生错误: %v", err)
	}
	if ipExists {
		return nil, fmt.Errorf("服务器 %s:%d 已存在，请检查IP地址和端口", req.IP, req.Port)
	}

	// 使用统一的验证方法
	if err := s.validateServerRequest(req.IP, req.Port, req.AuthType, req.Password, req.KeyFile); err != nil {
		return nil, err
	}

	return s.repo.Create(req)
}

// UpdateServer 更新服务器
func (s *ServerService) UpdateServer(id int, req *models.UpdateServerRequest) error {
	// 验证服务器名称是否与其他服务器重复
	exists, err := s.repo.ExistsByNameExcludeID(req.Name, id)
	if err != nil {
		return fmt.Errorf("检查服务器名称时发生错误: %v", err)
	}
	if exists {
		return fmt.Errorf("服务器名称 \"%s\" 已存在，请使用其他名称", req.Name)
	}

	// 验证IP地址和端口是否与其他服务器重复
	ipExists, err := s.repo.ExistsByIPAndPortExcludeID(req.IP, req.Port, id)
	if err != nil {
		return fmt.Errorf("检查服务器IP和端口时发生错误: %v", err)
	}
	if ipExists {
		return fmt.Errorf("服务器 %s:%d 已存在，请检查IP地址和端口", req.IP, req.Port)
	}

	// 使用统一的验证方法
	if err := s.validateServerRequest(req.IP, req.Port, req.AuthType, req.Password, req.KeyFile); err != nil {
		return err
	}

	return s.repo.Update(id, req)
}

// validateServerRequest 统一的服务器请求验证
func (s *ServerService) validateServerRequest(ip string, port int, authType, password, keyFile string) error {
	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		return fmt.Errorf("IP地址格式不正确")
	}

	// 验证端口范围
	if port < 1 || port > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}

	// 验证认证类型
	if authType != "password" && authType != "key" {
		return fmt.Errorf("认证类型必须是 password 或 key")
	}

	// 验证认证信息
	if authType == "password" && password == "" {
		return fmt.Errorf("密码认证时密码不能为空")
	}

	if authType == "key" && keyFile == "" {
		return fmt.Errorf("密钥认证时密钥文件路径不能为空")
	}

	return nil
}

// DeleteServer 删除服务器
func (s *ServerService) DeleteServer(id int) error {
	return s.repo.Delete(id)
}

// ToggleServerSelection 切换服务器选择状态
func (s *ServerService) ToggleServerSelection(id int) error {
	return s.repo.ToggleSelection(id)
}

// SelectAllServers 全选/取消全选服务器
func (s *ServerService) SelectAllServers(selected bool) error {
	return s.repo.SelectAll(selected)
}

// GetServerStats 获取服务器统计信息
func (s *ServerService) GetServerStats() (*models.ServerStats, error) {
	return s.repo.GetStats()
}

// TestServerConnection 测试已保存的服务器连接
func (s *ServerService) TestServerConnection(id int) (*models.TestConnectionResult, error) {
	server, err := s.repo.GetByID(id)
	if err != nil {
		return &models.TestConnectionResult{
			ServerID: id,
			Success:  false,
			Message:  "服务器不存在",
			Status:   "offline",
		}, nil
	}

	// 执行连接测试
	success, message := s.performConnectionTest(server)

	// 更新服务器状态
	status := "offline"
	if success {
		status = "online"
	}

	err = s.repo.UpdateStatus(id, status)
	if err != nil {
		// 记录错误但不影响测试结果
		fmt.Printf("更新服务器状态失败: %v\n", err)
	}

	return &models.TestConnectionResult{
		ServerID: id,
		Success:  success,
		Message:  message,
		Status:   status,
	}, nil
}

// TestBatchConnection 批量测试连接
func (s *ServerService) TestBatchConnection() ([]models.TestConnectionResult, error) {
	selectedServers, err := s.repo.GetSelectedServers()
	if err != nil {
		return nil, fmt.Errorf("获取已选择服务器失败: %v", err)
	}
	
	if len(selectedServers) == 0 {
		return []models.TestConnectionResult{}, nil
	}
	
	results := make([]models.TestConnectionResult, 0, len(selectedServers))
	
	// 并发测试连接
	resultChan := make(chan *models.TestConnectionResult, len(selectedServers))
	
	for _, server := range selectedServers {
		go func(srv models.Server) {
			result, _ := s.TestServerConnection(srv.ID)
			resultChan <- result
		}(server)
	}
	
	// 收集结果
	for i := 0; i < len(selectedServers); i++ {
		result := <-resultChan
		results = append(results, *result)
	}
	
	return results, nil
}

// performConnectionTest 执行实际的连接测试
func (s *ServerService) performConnectionTest(server *models.Server) (bool, string) {
	// 如果是SSH端口且有用户名，直接进行SSH连接测试（包含网络连接测试）
	if server.Port == 22 && server.Username != "" {
		return s.testSSHConnection(server)
	}

	// 对于非SSH端口，进行基本网络连接测试
	address := net.JoinHostPort(server.IP, strconv.Itoa(server.Port))
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return false, fmt.Sprintf("❌ %s (%s:%d) 网络连接失败: %v", server.Name, server.IP, server.Port, err)
	}
	conn.Close()

	return true, fmt.Sprintf("✅ %s (%s:%d) 网络连接成功", server.Name, server.IP, server.Port)
}

// testSSHConnection 测试SSH连接
func (s *ServerService) testSSHConnection(server *models.Server) (bool, string) {
	config := &ssh.ClientConfig{
		User:            server.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Timeout:         10 * time.Second,
	}
	
	// 根据认证类型设置认证方法
	switch server.AuthType {
	case "password":
		if server.Password == "" {
			return false, fmt.Sprintf("❌ %s (%s:%d) 密码不能为空", server.Name, server.IP, server.Port)
		}
		config.Auth = []ssh.AuthMethod{
			ssh.Password(server.Password),
		}
	case "key":
		if server.KeyFile == "" {
			return false, fmt.Sprintf("❌ %s (%s:%d) 密钥文件路径不能为空", server.Name, server.IP, server.Port)
		}
		// 这里应该读取密钥文件，为了演示简化处理
		return false, fmt.Sprintf("❌ %s (%s:%d) 密钥认证暂未实现", server.Name, server.IP, server.Port)
	default:
		return false, fmt.Sprintf("❌ %s (%s:%d) 不支持的认证类型", server.Name, server.IP, server.Port)
	}
	
	address := net.JoinHostPort(server.IP, strconv.Itoa(server.Port))
	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		return false, fmt.Sprintf("❌ %s (%s:%d) %v", server.Name, server.IP, server.Port, err)
	}
	defer client.Close()
	
	// 执行一个简单的命令来验证连接
	session, err := client.NewSession()
	if err != nil {
		return false, fmt.Sprintf("❌ %s (%s:%d) 会话创建失败", server.Name, server.IP, server.Port)
	}
	defer session.Close()
	
	output, err := session.Output("echo 'connection test'")
	if err != nil {
		return false, fmt.Sprintf("❌ %s (%s:%d) 执行测试命令失败: %v", server.Name, server.IP, server.Port, err)
	}
	
	if string(output) != "connection test\n" {
		return false, fmt.Sprintf("❌ %s (%s:%d) SSH连接测试失败", server.Name, server.IP, server.Port)
	}
	
	return true, fmt.Sprintf("✅ %s (%s:%d) SSH连接成功", server.Name, server.IP, server.Port)
}



// TestConnection 纯粹的连接测试功能（基于连接配置）
func (s *ServerService) TestConnection(config *models.ConnectionConfig) models.TestConnectionResult {
	// 验证连接配置
	if err := s.validateConnectionConfig(config); err != nil {
		return models.TestConnectionResult{
			ServerID: 0,
			Success:  false,
			Message:  err.Error(),
			Status:   "offline",
		}
	}

	// 执行连接测试
	success, message := s.performConnectionTestWithConfig(config)

	status := "offline"
	if success {
		status = "online"
	}

	return models.TestConnectionResult{
		ServerID: 0, // 纯测试，无服务器ID
		Success:  success,
		Message:  message,
		Status:   status,
	}
}

// validateConnectionConfig 验证连接配置
func (s *ServerService) validateConnectionConfig(config *models.ConnectionConfig) error {
	if net.ParseIP(config.IP) == nil {
		return fmt.Errorf("IP地址格式不正确")
	}

	if config.Port < 1 || config.Port > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}

	if config.AuthType != "password" && config.AuthType != "key" {
		return fmt.Errorf("认证类型必须是 password 或 key")
	}

	if config.AuthType == "password" && config.Password == "" {
		return fmt.Errorf("密码认证时密码不能为空")
	}

	if config.AuthType == "key" && config.KeyFile == "" {
		return fmt.Errorf("密钥认证时密钥文件路径不能为空")
	}

	return nil
}

// performConnectionTestWithConfig 基于连接配置执行连接测试
func (s *ServerService) performConnectionTestWithConfig(config *models.ConnectionConfig) (bool, string) {
	// 如果是SSH端口且有用户名，直接进行SSH连接测试（包含网络连接测试）
	if config.Port == 22 && config.Username != "" {
		return s.testSSHConnectionWithConfig(config)
	}

	// 对于非SSH端口，进行基本网络连接测试
	address := net.JoinHostPort(config.IP, strconv.Itoa(config.Port))
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return false, fmt.Sprintf("❌ %s:%d 网络连接失败: %v", config.IP, config.Port, err)
	}
	conn.Close()

	return true, fmt.Sprintf("✅ %s:%d 网络连接成功", config.IP, config.Port)
}

// testSSHConnectionWithConfig 基于连接配置测试SSH连接
func (s *ServerService) testSSHConnectionWithConfig(config *models.ConnectionConfig) (bool, string) {
	var auth ssh.AuthMethod

	// 根据认证类型设置认证方法
	if config.AuthType == "password" {
		auth = ssh.Password(config.Password)
	} else if config.AuthType == "key" {
		// 读取私钥文件
		keyData, err := os.ReadFile(config.KeyFile)
		if err != nil {
			return false, fmt.Sprintf("❌ 读取密钥文件失败: %v", err)
		}

		var signer ssh.Signer
		if config.KeyPassphrase != "" {
			// 有密码保护的私钥
			signer, err = ssh.ParsePrivateKeyWithPassphrase(keyData, []byte(config.KeyPassphrase))
		} else {
			// 无密码保护的私钥
			signer, err = ssh.ParsePrivateKey(keyData)
		}

		if err != nil {
			return false, fmt.Sprintf("❌ 解析私钥失败: %v", err)
		}

		auth = ssh.PublicKeys(signer)
	} else {
		return false, "❌ 不支持的认证类型"
	}

	// SSH客户端配置
	sshConfig := &ssh.ClientConfig{
		User: config.Username,
		Auth: []ssh.AuthMethod{auth},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Timeout:         10 * time.Second,
	}

	// 连接SSH服务器
	address := net.JoinHostPort(config.IP, strconv.Itoa(config.Port))
	client, err := ssh.Dial("tcp", address, sshConfig)
	if err != nil {
		return false, fmt.Sprintf("❌ %s:%d %v", config.IP, config.Port, err)
	}
	defer client.Close()

	// 创建会话并执行简单命令测试
	session, err := client.NewSession()
	if err != nil {
		return false, fmt.Sprintf("❌ %s:%d 会话创建失败", config.IP, config.Port)
	}
	defer session.Close()

	// 执行简单的echo命令测试
	output, err := session.Output("echo 'connection_test'")
	if err != nil {
		return false, fmt.Sprintf("❌ 执行测试命令失败: %v", err)
	}

	if strings.TrimSpace(string(output)) != "connection_test" {
		return false, "❌ SSH命令执行结果异常"
	}

	return true, "✅ SSH连接成功"
}


