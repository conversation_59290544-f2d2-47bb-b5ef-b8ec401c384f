package models

import (
	"time"
)

// Server 服务器模型
type Server struct {
	ID            int       `json:"id" db:"id"`
	Name          string    `json:"name" db:"name"`
	IP            string    `json:"ip" db:"ip"`
	Port          int       `json:"port" db:"port"`
	Username      string    `json:"username" db:"username"`
	AuthType      string    `json:"authType" db:"auth_type"` // password, key
	Password      string    `json:"password,omitempty" db:"password"`
	KeyFile       string    `json:"keyFile,omitempty" db:"key_file"`
	KeyPassphrase string    `json:"keyPassphrase,omitempty" db:"key_passphrase"`
	Status        string    `json:"status" db:"status"` // online, offline
	Selected      bool      `json:"selected" db:"selected"`
	LastCheck     *time.Time `json:"lastCheck,omitempty" db:"last_check"`
	CreatedAt     time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt     time.Time `json:"updatedAt" db:"updated_at"`
}

// ServerStats 服务器统计信息
type ServerStats struct {
	Total    int `json:"total"`
	Selected int `json:"selected"`
	Online   int `json:"online"`
	Offline  int `json:"offline"`
}

// TestConnectionResult 连接测试结果
type TestConnectionResult struct {
	ServerID int    `json:"serverId"`
	Success  bool   `json:"success"`
	Message  string `json:"message"`
	Status   string `json:"status"`
}

// CreateServerRequest 创建服务器请求
type CreateServerRequest struct {
	Name          string `json:"name" validate:"required"`
	IP            string `json:"ip" validate:"required,ip"`
	Port          int    `json:"port" validate:"required,min=1,max=65535"`
	Username      string `json:"username" validate:"required"`
	AuthType      string `json:"authType" validate:"required,oneof=password key"`
	Password      string `json:"password,omitempty"`
	KeyFile       string `json:"keyFile,omitempty"`
	KeyPassphrase string `json:"keyPassphrase,omitempty"`
}

// UpdateServerRequest 更新服务器请求
type UpdateServerRequest struct {
	Name          string `json:"name" validate:"required"`
	IP            string `json:"ip" validate:"required,ip"`
	Port          int    `json:"port" validate:"required,min=1,max=65535"`
	Username      string `json:"username" validate:"required"`
	AuthType      string `json:"authType" validate:"required,oneof=password key"`
	Password      string `json:"password,omitempty"`
	KeyFile       string `json:"keyFile,omitempty"`
	KeyPassphrase string `json:"keyPassphrase,omitempty"`
}

// ConnectionConfig 连接配置（专用于测试连接）
type ConnectionConfig struct {
	IP            string `json:"ip" validate:"required,ip"`
	Port          int    `json:"port" validate:"required,min=1,max=65535"`
	Username      string `json:"username" validate:"required"`
	AuthType      string `json:"authType" validate:"required,oneof=password key"`
	Password      string `json:"password,omitempty"`
	KeyFile       string `json:"keyFile,omitempty"`
	KeyPassphrase string `json:"keyPassphrase,omitempty"`
}

// TableName 返回表名
func (Server) TableName() string {
	return "servers"
}
