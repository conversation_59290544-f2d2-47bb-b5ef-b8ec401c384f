package database

import (
	"database/sql"
	"fmt"
	"path/filepath"

	_ "modernc.org/sqlite"
)

// DB 数据库实例
var DB *sql.DB

// InitDatabase 初始化数据库
func InitDatabase(dataDir string) error {
	// 创建数据库文件路径
	dbPath := filepath.Join(dataDir, "guardx.db")

	// 打开数据库连接
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	DB = db

	// 创建表
	if err := createTables(); err != nil {
		return fmt.Errorf("创建数据表失败: %v", err)
	}

	return nil
}

// createTables 创建数据表
func createTables() error {
	// 创建服务器表
	serverTableSQL := `
	CREATE TABLE IF NOT EXISTS servers (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		ip TEXT NOT NULL,
		port INTEGER NOT NULL DEFAULT 22,
		username TEXT NOT NULL DEFAULT 'root',
		auth_type TEXT NOT NULL DEFAULT 'password',
		password TEXT,
		key_file TEXT,
		key_passphrase TEXT,
		status TEXT NOT NULL DEFAULT 'offline',
		selected BOOLEAN NOT NULL DEFAULT FALSE,
		last_check DATETIME,
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(ip, port)
	);`
	
	if _, err := DB.Exec(serverTableSQL); err != nil {
		return fmt.Errorf("创建服务器表失败: %v", err)
	}
	
	// 创建检查配置表
	checkConfigTableSQL := `
	CREATE TABLE IF NOT EXISTS check_configs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		category TEXT NOT NULL,
		name TEXT NOT NULL,
		level TEXT NOT NULL,
		enabled BOOLEAN NOT NULL DEFAULT TRUE,
		description TEXT,
		script_path TEXT,
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
	);`
	
	if _, err := DB.Exec(checkConfigTableSQL); err != nil {
		return fmt.Errorf("创建检查配置表失败: %v", err)
	}
	
	// 创建任务表
	taskTableSQL := `
	CREATE TABLE IF NOT EXISTS tasks (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		description TEXT,
		status TEXT NOT NULL DEFAULT 'pending',
		progress INTEGER NOT NULL DEFAULT 0,
		total_servers INTEGER NOT NULL DEFAULT 0,
		completed_servers INTEGER NOT NULL DEFAULT 0,
		success_count INTEGER NOT NULL DEFAULT 0,
		warning_count INTEGER NOT NULL DEFAULT 0,
		error_count INTEGER NOT NULL DEFAULT 0,
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		started_at DATETIME,
		completed_at DATETIME,
		updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
	);`
	
	if _, err := DB.Exec(taskTableSQL); err != nil {
		return fmt.Errorf("创建任务表失败: %v", err)
	}
	
	// 创建任务服务器关联表
	taskServerTableSQL := `
	CREATE TABLE IF NOT EXISTS task_servers (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_id TEXT NOT NULL,
		server_id INTEGER NOT NULL,
		status TEXT NOT NULL DEFAULT 'pending',
		result TEXT,
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
		FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
		UNIQUE(task_id, server_id)
	);`
	
	if _, err := DB.Exec(taskServerTableSQL); err != nil {
		return fmt.Errorf("创建任务服务器关联表失败: %v", err)
	}
	
	// 创建检查结果表
	checkResultTableSQL := `
	CREATE TABLE IF NOT EXISTS check_results (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_id TEXT NOT NULL,
		server_id INTEGER NOT NULL,
		check_config_id INTEGER NOT NULL,
		status TEXT NOT NULL,
		message TEXT,
		details TEXT,
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
		FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
		FOREIGN KEY (check_config_id) REFERENCES check_configs(id) ON DELETE CASCADE
	);`
	
	if _, err := DB.Exec(checkResultTableSQL); err != nil {
		return fmt.Errorf("创建检查结果表失败: %v", err)
	}
	
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// GetDB 获取数据库实例
func GetDB() *sql.DB {
	return DB
}
